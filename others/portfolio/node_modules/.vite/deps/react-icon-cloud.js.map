{"version": 3, "sources": ["../../@csstools/convert-colors/src/util.js", "../../@csstools/convert-colors/src/rgb-hsl.js", "../../@csstools/convert-colors/src/rgb-hwb.js", "../../@csstools/convert-colors/src/rgb-hsv.js", "../../@csstools/convert-colors/src/rgb-xyz.js", "../../@csstools/convert-colors/src/hsl-hsv.js", "../../@csstools/convert-colors/src/hwb-hsv.js", "../../@csstools/convert-colors/src/lab-xyz.js", "../../@csstools/convert-colors/src/lab-lch.js", "../../@csstools/convert-colors/src/rgb-contrast.js", "../../@csstools/convert-colors/src/hex-rgb.js", "../../@csstools/convert-colors/src/keyword-rgb.js", "../../@csstools/convert-colors/src/lab-ciede.js", "../../@csstools/convert-colors/src/index.js", "../../react-icon-cloud/src/lib/tag_canvas_string.ts", "../../react-icon-cloud/src/utils/guid.ts", "../../react-icon-cloud/src/utils/use_in_viewport.tsx", "../../react-icon-cloud/src/renderers/cloud.tsx", "../../react-icon-cloud/src/utils/add_hash.ts", "../../react-icon-cloud/src/renderers/simple_icon.tsx", "../../react-icon-cloud/node_modules/regenerator-runtime/runtime.js", "../../react-icon-cloud/src/utils/svg_to_path.ts", "../../react-icon-cloud/src/utils/get_slugs_path.ts", "../../react-icon-cloud/src/utils/get_slug.ts", "../../react-icon-cloud/src/utils/get_slug_hexs.ts", "../../react-icon-cloud/src/utils/fetch_simple_icons.ts"], "sourcesContent": ["/**\n* @private\n* @func rgb2hue\n* @desc Return a hue angle from an RGB color\n* @param {Number} r - Red (0 - 100)\n* @param {Number} g - Red (0 - 100)\n* @param {Number} b - Red (0 - 100)\n* @param {Number} f - Hue Fallback (0 - 360)\n* @return {Number} Hue <PERSON>le (0 - 360)\n* @example\n* rgb2hue(100, 0, 0)\n* @example\n* rgb2hue(100, 0, 0, 0)\n*/\n\nexport function rgb2hue(rgbR, rgbG, rgbB, fallbackhue = 0) {\n\tconst value     = rgb2value(rgbR, rgbG, rgbB);\n\tconst whiteness = rgb2whiteness(rgbR, rgbG, rgbB);\n\tconst delta     = value - whiteness;\n\n\tif (delta) {\n\t\t// calculate segment\n\t\tconst segment = value === rgbR\n\t\t\t? (rgbG - rgbB) / delta\n\t\t: value === rgbG\n\t\t\t? (rgbB - rgbR) / delta\n\t\t: (rgbR - rgbG) / delta;\n\n\t\t// calculate shift\n\t\tconst shift = value === rgbR\n\t\t\t? segment < 0\n\t\t\t\t? 360 / 60\n\t\t\t\t: 0 / 60\n\t\t: value === rgbG\n\t\t\t? 120 / 60\n\t\t: 240 / 60;\n\n\t\t// calculate hue\n\t\tconst hue = (segment + shift) * 60;\n\n\t\treturn hue;\n\t} else {\n\t\t// otherwise return the Hue Fallback\n\t\treturn fallbackhue;\n\t}\n}\n\n/**\n* @private\n* @func hue2rgb\n* @desc Return an RGB channel from a hue angle\n* @param {Number} t1\n* @param {Number} t2\n* @param {Number} h - Hue Angle (0 - 360)\n* @return {Number} RGB channel (0 - 100)\n* @example\n* hue2rgb(0, 0, 0)\n*/\n\nexport function hue2rgb(t1, t2, hue) {\n\t// calculate the ranged hue\n\tconst rhue = hue < 0 ? hue + 360 : hue > 360 ? hue - 360 : hue;\n\n\t// calculate the rgb value\n\tconst rgb = rhue * 6 < 360\n\t\t? t1 + (t2 - t1) * rhue / 60\n\t: rhue * 2 < 360\n\t\t? t2\n\t: rhue * 3 < 720\n\t\t? t1 + (t2 - t1) * (240 - rhue) / 60\n\t: t1;\n\n\treturn rgb;\n}\n\n/**\n* @private\n* @func luminance2contrast\n* @desc Return the contrast ratio between 2 luminance.\n* @param {Number} l1 - Relative luminance of one color\n* @param {Number} l2 - Relative luminance of another color\n* @return {Number} Contrast ratio between the 2 luminance\n* @example\n* luminance2contrast(0.2126, 0) // => 5.252\n* @link https://www.w3.org/TR/WCAG21/#dfn-contrast-ratio\n*/\n\nexport function luminance2contrast(relativeLuminance1, relativeLuminance2) {\n\t// l1 is the relative luminance of the lighter of the colors\n\tconst l1 = max(relativeLuminance1, relativeLuminance2);\n\n\t// l1 is the relative luminance of the darker of the colors\n\tconst l2 = min(relativeLuminance1, relativeLuminance2);\n\n\treturn (l1 * precision + 0.05 * precision) / (l2 * precision + 0.05 * precision);\n}\n\n\n/* RGB tooling\n/* ========================================================================== */\n\nexport function rgb2value(rgbR, rgbG, rgbB) {\n\tconst value = max(rgbR, rgbG, rgbB);\n\n\treturn value;\n}\n\nexport function rgb2whiteness(rgbR, rgbG, rgbB) {\n\tconst whiteness = min(rgbR, rgbG, rgbB);\n\n\treturn whiteness;\n}\n\n/* Math matrix\n/* ========================================================================== */\n\nexport function matrix(params, mats) {\n\treturn mats.map(\n\t\tmat => mat.reduce(\n\t\t\t// (acc, value, index) => acc + params[index] * value,\n\t\t\t(acc, value, index) => acc + params[index] * precision * (value * precision) / precision / precision,\n\t\t\t0\n\t\t)\n\t);\n}\n\n/* Precision\n/* ========================================================================== */\n\nexport const precision = 100000000;\n\n/* D50 reference white\n/* ========================================================================== */\n\nexport const [ wd50X, wd50Y, wd50Z ] = [ 96.42, 100, 82.49 ];\n\n/* Math Expressions\n/* ========================================================================== */\n\nexport const atan2d = (y, x) => rad2deg(atan2(y, x)); // arc-tangent in degrees\nexport const cosd = x => cos(deg2rad(x)); // cosine of the specified angle in degrees\nexport const deg2rad = x => x * PI / 180; // degree to radian\nexport const rad2deg = x => x * 180 / PI; // radian to degree\nexport const sind = x => sin(deg2rad(x)); // sine in degrees\n\n/* Math Constants\n/* ========================================================================== */\n\nexport const abs = Math.abs;\nexport const atan2 = Math.atan2;\nexport const cbrt = Math.cbrt;\nexport const cos = Math.cos;\nexport const exp = Math.exp;\nexport const floor = Math.floor;\nexport const max = Math.max;\nexport const min = Math.min;\nexport const PI = Math.PI;\nexport const pow = Math.pow;\nexport const sin = Math.sin;\nexport const sqrt = Math.sqrt;\n\nexport const epsilon = pow(6, 3) / pow(29, 3);\nexport const kappa = pow(29, 3) / pow(3, 3);\n", "import { abs, rgb2hue, rgb2value, rgb2whiteness, hue2rgb } from './util';\n\n/**\n* @func rgb2hsl\n* @desc Return a HSL color from an RGB color\n* @param {Number} r - red (0 - 100)\n* @param {Number} g - green (0 - 100)\n* @param {Number} b - blue (0 - 100)\n* @param {Number=} f - Hue Fallback (0 - 360)\n* @return {ArrayHSL}\n* @example\n* rgb2hsl(0, 100, 100) // => [0, 100, 50]\n* @link https://www.w3.org/TR/css-color-3/#hsl-color\n* @link https://www.w3.org/TR/css-color-4/#hsl-to-rgb\n* @link https://www.rapidtables.com/convert/color/rgb-to-hsl.html\n* @link https://www.rapidtables.com/convert/color/hsl-to-rgb.html\n*/\n\nexport function rgb2hsl(rgbR, rgbG, rgbB, fallbackhue) {\n\tconst hslH = rgb2hue(rgbR, rgbG, rgbB, fallbackhue);\n\tconst hslV = rgb2value(rgbR, rgbG, rgbB);\n\tconst hslW = rgb2whiteness(rgbR, rgbG, rgbB);\n\n\t// calculate value/whiteness delta\n\tconst hslD = hslV - hslW;\n\n\t// calculate lightness\n\tconst hslL = (hslV + hslW) / 2;\n\n\t// calculate saturation\n\tconst hslS = hslD === 0 ? 0 : hslD / (100 - abs(2 * hslL - 100)) * 100;\n\n\treturn [ hslH, hslS, hslL ];\n}\n\n/**\n* @func hsl2rgb\n* @desc Return an RGB color from an HSL color\n* @param {Number} h - Hue Angle (0 - 360)\n* @param {Number} s - Saturation (0 - 100)\n* @param {Number} l - Lightness (0 - 100)\n* @return {ArrayRGB}\n* @example\n* hsl2rgb(0, 100, 50) // => [0, 100, 100]\n* @link https://www.w3.org/TR/css-color-3/#hsl-color\n* @link https://www.w3.org/TR/css-color-4/#hsl-to-rgb\n* @link https://www.rapidtables.com/convert/color/rgb-to-hsl.html\n* @link https://www.rapidtables.com/convert/color/hsl-to-rgb.html\n*/\n\nexport function hsl2rgb(hslH, hslS, hslL) {\n\t// calcuate t2\n\tconst t2 = hslL <= 50 ? hslL * (hslS + 100) / 100 : hslL + hslS - hslL * hslS / 100;\n\n\t// calcuate t1\n\tconst t1 = hslL * 2 - t2;\n\n\t// calculate rgb\n\tconst [ rgbR, rgbG, rgbB ] = [\n\t\thue2rgb(t1, t2, hslH + 120),\n\t\thue2rgb(t1, t2, hslH),\n\t\thue2rgb(t1, t2, hslH - 120)\n\t];\n\n\treturn [ rgbR, rgbG, rgbB ];\n}\n", "import { rgb2hue, rgb2whiteness, rgb2value } from './util';\nimport { hsl2rgb } from './rgb-hsl';\n\n/**\n* @func rgb2hwb\n* @desc Return an HWB color from an RGB color\n* @param {Number} r - Red (0 - 100)\n* @param {Number} g - Green (0 - 100)\n* @param {Number} b - Blue (0 - 100)\n* @param {Number} f - Hue Fallback (0 - 360)\n* @return {ArrayHWB}\n* @example\n* rgb2hwb(100, 0, 0) // => [0, 0, 0]\n* @link https://www.w3.org/TR/css-color-4/#hwb-to-rgb\n* @link http://alvyray.com/Papers/CG/hwb2rgb.htm\n*/\n\nexport function rgb2hwb(rgbR, rgbG, rgbB, fallbackhue) {\n\tconst hwbH = rgb2hue(rgbR, rgbG, rgbB, fallbackhue);\n\tconst hwbW = rgb2whiteness(rgbR, rgbG, rgbB);\n\tconst hwbV = rgb2value(rgbR, rgbG, rgbB);\n\tconst hwbB = 100 - hwbV;\n\n\treturn [hwbH, hwbW, hwbB];\n}\n\n/**\n* @func hwb2rgb\n* @desc Return an RGB color from an HWB color\n* @param {Number} h - Hue Angle (0 - 360)\n* @param {Number} w - Whiteness (0 - 100)\n* @param {Number} b - Blackness (0 - 100)\n* @return {ArrayRGB}\n* @example\n* hwb2rgb(0, 0, 0) // => [100, 0, 0]\n* @link https://www.w3.org/TR/css-color-4/#hwb-to-rgb\n* @link http://alvyray.com/Papers/CG/hwb2rgb.htm\n*/\n\nexport function hwb2rgb(hwbH, hwbW, hwbB, fallbackhue) {\n\tconst [ rgbR, rgbG, rgbB ] = hsl2rgb(hwbH, 100, 50, fallbackhue).map(\n\t\tv => v * (100 - hwbW - hwbB) / 100 + hwbW\n\t);\n\n\treturn [ rgbR, rgbG, rgbB ];\n}\n", "import { floor, rgb2value, rgb2whiteness, rgb2hue } from './util';\n\n/**\n* @func rgb2hsv\n* @desc Return an HSV color from an RGB color\n* @param {Number} h - Hue Angle (0 - 360)\n* @param {Number} s - Saturation (0 - 100)\n* @param {Number} v - Value (0 - 100)\n* @param {Number=} f - Hue Fallback (0 - 360)\n* @return {ArrayHSV}\n* @example\n* rgb2hsv(100, 0, 0) // => [0, 100, 100]\n* @link http://alvyray.com/Papers/CG/hsv2rgb.htm\n*/\n\nexport function rgb2hsv(rgbR, rgbG, rgbB, fallbackhue) {\n\tconst hsvV = rgb2value(rgbR, rgbG, rgbB);\n\tconst hsvW = rgb2whiteness(rgbR, rgbG, rgbB);\n\tconst hsvH = rgb2hue(rgbR, rgbG, rgbB, fallbackhue);\n\n\t// calculate saturation\n\tconst hsvS = hsvV === hsvW ? 0 : (hsvV - hsvW) / hsvV * 100;\n\n\treturn [ hsvH, hsvS, hsvV ];\n}\n\n/**\n* @func hsv2rgb\n* @desc Return an RGB color from an HSV color\n* @param {Number} h - Hue Angle (0 - 360)\n* @param {Number} s - Saturation (0 - 100)\n* @param {Number} v - Value (0 - 100)\n* @return {ArrayRGB}\n* @example\n* hsv2rgb(100, 0, 0) // => [100, 0, 0]\n* @link http://alvyray.com/Papers/CG/hsv2rgb.htm\n*/\n\nexport function hsv2rgb(hsvH, hsvS, hsvV) {\n\tconst rgbI = floor(hsvH / 60);\n\n\t// calculate rgb parts\n\tconst rgbF = hsvH / 60 - rgbI & 1 ? hsvH / 60 - rgbI : 1 - hsvH / 60 - rgbI;\n\tconst rgbM = hsvV * (100 - hsvS) / 100;\n\tconst rgbN = hsvV * (100 - hsvS * rgbF) / 100;\n\n\tconst [ rgbR, rgbG, rgbB ] = rgbI === 5\n\t\t? [ hsvV, rgbM, rgbN ]\n\t: rgbI === 4\n\t\t? [ rgbN, rgbM, hsvV ]\n\t: rgbI === 3\n\t\t? [ rgbM, rgbN, hsvV ]\n\t: rgbI === 2\n\t\t? [ rgbM, hsvV, rgbN ]\n\t: rgbI === 1\n\t\t? [ rgbN, hsvV, rgbM ]\n\t: [ hsvV, rgbN, rgbM ];\n\n\treturn [ rgbR, rgbG, rgbB ];\n}\n", "import { matrix, pow } from './util';\n\n/**\n* @func rgb2xyz\n* @desc Return an XYZ color from an RGB color\n* @param {Number} r - Red (0 - 100)\n* @param {Number} g - Green (0 - 100)\n* @param {Number} b - Blue (0 - 100)\n* @return {ArrayXYZ}\n* @example\n* rgb2xyz(100, 0, 0) // => [41.25, 21.27, 1.93]\n* @link https://www.w3.org/TR/css-color-4/#rgb-to-lab\n* @link https://www.w3.org/TR/css-color-4/#color-conversion-code\n*/\n\nexport function rgb2xyz(rgbR, rgbG, rgbB) {\n\tconst [ lrgbR, lrgbB, lrgbG ] = [ rgbR, rgbG, rgbB ].map(\n\t\tv => v > 4.045 ? pow((v + 5.5) / 105.5, 2.4) * 100 : v / 12.92\n\t);\n\n\tconst [ xyzX, xyzY, xyzZ ] = matrix([ lrgbR, lrgbB, lrgbG ], [\n\t\t[0.4124564, 0.3575761, 0.1804375],\n\t\t[0.2126729, 0.7151522, 0.0721750],\n\t\t[0.0193339, 0.1191920, 0.9503041]\n\t]);\n\n\treturn [ xyzX, xyzY, xyzZ ];\n}\n\n/**\n* @func xyz2rgb\n* @desc Return an XYZ color from an RGB color\n* @param {Number} x - Chromaticity of X\n* @param {Number} y - Chromaticity of Y\n* @param {Number} z - Chromaticity of Z\n* @return {ArrayRGB}\n* @example\n* xyz2rgb(41.25, 21.27, 1.93) // => [100, 0, 0]\n* @link https://www.w3.org/TR/css-color-4/#rgb-to-lab\n* @link https://www.w3.org/TR/css-color-4/#color-conversion-code\n*/\n\nexport function xyz2rgb(xyzX, xyzY, xyzZ) {\n\tconst [ lrgbR, lrgbB, lrgbG ] = matrix([ xyzX, xyzY, xyzZ ], [\n\t\t[ 3.2404542, -1.5371385, -0.4985314],\n\t\t[-0.9692660,  1.8760108,  0.0415560],\n\t\t[ 0.0556434, -0.2040259,  1.0572252]\n\t]);\n\n\tconst [ rgbR, rgbG, rgbB ] = [ lrgbR, lrgbB, lrgbG ].map(\n\t\tv => v > 0.31308 ? 1.055 * pow(v / 100, 1 / 2.4) * 100 - 5.5 : 12.92 * v\n\t);\n\n\treturn [ rgbR, rgbG, rgbB ];\n}\n", "/**\n* @func hsl2hsv\n* @desc Return an HSV color from an HSL color\n* @param {Number} h - <PERSON><PERSON> (0 - 360)\n* @param {Number} s - Saturation (0 - 100)\n* @param {Number} l - Lightness (0 - 100)\n* @return {ArrayHSV}\n* @example\n* hsl2hsv(0, 100, 50)\n* @link https://gist.github.com/defims/0ca2ef8832833186ed396a2f8a204117\n*/\n\nexport function hsl2hsv(hslH, hslS, hslL) {\n\tconst hsv1 = hslS * (hslL < 50 ? hslL : 100 - hslL) / 100;\n\tconst hsvS = hsv1 === 0 ? 0 : 2 * hsv1 / (hslL + hsv1) * 100;\n\tconst hsvV = hslL + hsv1;\n\n\treturn [ hslH, hsvS, hsvV ];\n}\n\n/**\n* @func hsv2hsl\n* @desc Return an HSL color from an HSV color\n* @param {Number} h - <PERSON><PERSON> (0 - 360)\n* @param {Number} s - Saturation (0 - 100)\n* @param {Number} v - Value (0 - 100)\n* @return {ArrayHSL}\n* @example\n* hsv2hsl(0, 0, 0) // => [0, 100, 50]\n* @link https://gist.github.com/defims/0ca2ef8832833186ed396a2f8a204117\n*/\n\nexport function hsv2hsl(hsvH, hsvS, hsvV) {\n\tconst hslL = (200 - hsvS) * hsvV / 100;\n\n\tconst [ hslS, hslV ] = [\n\t\thslL === 0 || hslL === 200 ? 0 : hsvS * hsvV / 100 / (hslL <= 100 ? hslL : 200 - hslL) * 100,\n\t\thslL * 5 / 10\n\t];\n\n\treturn [ hsvH, hslS, hslV ];\n}\n", "/**\n* @func hwb2hsv\n* @desc Return an HSV color from an HWB color\n* @param {Number} h - <PERSON><PERSON> (0 - 360)\n* @param {Number} w - Whiteness (0 - 100)\n* @param {Number} b - Blackness (0 - 100)\n* @return {ArrayHSV}\n* @example\n* hwb2hsv(0, 0, 0) // => [0, 100, 100]\n* @link https://en.wikipedia.org/wiki/HWB_color_model#Converting_to_and_from_HSV\n*/\n\nexport function hwb2hsv(hwbH, hwbW, hwbB) {\n\tconst [ hsvH, hsvS, hsvV ] = [\n\t\thwbH,\n\t\thwbB === 100 ? 0 : 100 - hwbW / (100 - hwbB) * 100,\n\t\t100 - hwbB\n\t];\n\n\treturn [ hsvH, hsvS, hsvV ];\n}\n\n\n/**\n* @func hsv2hwb\n* @desc Return an HWB color from an HSV color\n* @param {Number} h - <PERSON><PERSON> (0 - 360)\n* @param {Number} s - Saturation (0 - 100)\n* @param {Number} v - Value (0 - 100)\n* @return {ArrayHWB}\n* @example\n* hsv2hwb(0, 100, 100) // => [0, 0, 0]\n* @link https://en.wikipedia.org/wiki/HWB_color_model#Converting_to_and_from_HSV\n*/\n\nexport function hsv2hwb(hsvH, hsvS, hsvV) {\n\tconst [ hwbH, hwbW, hwbB ] = [\n\t\thsvH,\n\t\t(100 - hsvS) * hsvV / 100,\n\t\t100 - hsvV\n\t];\n\n\treturn [ hwbH, hwbW, hwbB ];\n}\n", "import { cbrt, epsilon, kappa, pow, wd50X, wd50Y, wd50Z, matrix } from './util';\n\n/**\n* @func lab2xyz\n* @desc Return an XYZ color from a LAB color\n* @param {Number} l - CIE Lightness\n* @param {Number} a - Red/Green Coordinate\n* @param {Number} b - Yellow/Blue Coordinate\n* @return {ArrayXYZ}\n* @example\n* lab2xyz(54.29, 80.82, 69.88) // => 41.25, 21.27, 1.93\n* @link https://www.w3.org/TR/css-color-4/#rgb-to-lab\n* @link https://www.w3.org/TR/css-color-4/#color-conversion-code\n* @link https://www.easyrgb.com/en/math.php\n*/\n\nexport function lab2xyz(labL, labA, labB) {\n\t// compute f, starting with the luminance-related term\n\tconst f2 = (labL + 16) / 116;\n\tconst f1 = labA / 500 + f2;\n\tconst f3 = f2 - labB / 200;\n\n\t// compute pre-scaled XYZ\n\tconst [ initX, initY, initZ ] = [\n\t\tpow(f1, 3) > epsilon   ? pow(f1, 3)                : (116 * f1 - 16) / kappa,\n\t\tlabL > kappa * epsilon ? pow((labL + 16) / 116, 3) : labL / kappa,\n\t\tpow(f3, 3) > epsilon   ? pow(f3, 3)                : (116 * f3 - 16) / kappa\n\t];\n\n\tconst [ xyzX, xyzY, xyzZ ] = matrix(\n\t\t// compute XYZ by scaling pre-scaled XYZ by reference white\n\t\t[ initX * wd50X, initY * wd50Y, initZ * wd50Z ],\n\t\t// calculate D65 XYZ from D50 XYZ\n\t\t[\n\t\t\t[ 0.9555766, -0.0230393,  0.0631636],\n\t\t\t[-0.0282895,  1.0099416,  0.0210077],\n\t\t\t[ 0.0122982, -0.0204830,  1.3299098]\n\t\t]\n\t);\n\n\treturn [ xyzX, xyzY, xyzZ ];\n}\n\n/**\n* @func xyz2lab\n* @desc Return an LAB color from a XYZ color\n* @param {Number} x - Chromaticity of X\n* @param {Number} y - Chromaticity of Y\n* @param {Number} z - Chromaticity of Z\n* @return {ArrayLAB}\n* @example\n* xyz2lab(41.25, 21.27, 1.93) // => [54.29, 80.82, 69.88]\n* @link https://www.w3.org/TR/css-color-4/#rgb-to-lab\n* @link https://www.w3.org/TR/css-color-4/#color-conversion-code\n* @link https://www.easyrgb.com/en/math.php\n*/\n\nexport function xyz2lab(xyzX, xyzY, xyzZ) {\n\t// calculate D50 XYZ from D65 XYZ\n\tconst [ d50X, d50Y, d50Z ] = matrix([ xyzX, xyzY, xyzZ ], [\n\t\t[ 1.0478112,  0.0228866, -0.0501270],\n\t\t[ 0.0295424,  0.9904844, -0.0170491],\n\t\t[-0.0092345,  0.0150436,  0.7521316]\n\t]);\n\n\t// calculate f\n\tconst [ f1, f2, f3 ] = [\n\t\td50X / wd50X,\n\t\td50Y / wd50Y,\n\t\td50Z / wd50Z\n\t].map(\n\t\tvalue => value > epsilon ? cbrt(value) : (kappa * value + 16) / 116\n\t);\n\n\tconst [ labL, labA, labB ] = [\n\t\t116 * f2 - 16,\n\t\t500 * (f1 - f2),\n\t\t200 * (f2 - f3)\n\t];\n\n\treturn [ labL, labA, labB ];\n}\n", "import { atan2, cosd, pow, rad2deg, sind, sqrt } from './util';\n\n/**\n* @func lab2lch\n* @desc Return an LCH color from a LAB color\n* @param {Number} l - CIE Lightness\n* @param {Number} a - Red/Green Coordinate\n* @param {Number} b - Yellow/Blue Coordinate\n* @return {ArrayLAB}\n* @example\n* lab2lch(54.29, 80.82, 69.88) // => [54.29, 106.84, 40.85]\n* @link https://www.w3.org/TR/css-color-4/#color-conversion-code\n* @link https://www.w3.org/TR/css-color-4/#lch-to-lab\n*/\n\nexport function lab2lch(labL, labA, labB) {\n\tconst [ lchC, lchH ] = [\n\t\tsqrt(pow(labA, 2) + pow(labB, 2)), // convert to chroma\n\t\trad2deg(atan2(labB, labA)) // convert to hue, in degrees\n\t];\n\n\treturn [ labL, lchC, lchH ];\n}\n\n/**\n* @func lch2lab\n* @desc Return a LAB color from an LCH color\n* @param {Number} l - CIE Lightness\n* @param {Number} c - CIE Chroma\n* @param {Number} h - CIE Hue Angle\n* @return {ArrayLCH}\n* @example\n* lch2lab(54.29, 106.84, 40.85) // => [54.29, 80.82, 69.88]\n* @link https://www.w3.org/TR/css-color-4/#color-conversion-code\n* @link https://www.w3.org/TR/css-color-4/#lch-to-lab\n*/\n\nexport function lch2lab(lchL, lchC, lchH) {\n\t// convert to Lab a and b from the polar form\n\tconst [ labA, labB ] = [\n\t\tlchC * cosd(lchH),\n\t\tlchC * sind(lchH)\n\t];\n\n\treturn [ lchL, labA, labB ];\n}\n", "import { luminance2contrast, pow, precision } from './util'\n\n/**\n* @func rgb2contrast\n* @desc Return the contrast ratio of of RGB colors\n* @param {ArrayRGB} rgb1 - RGB Color Array\n* @param {ArrayRGB} rgb2 - RGB Color Array\n* @return {NumberContrast}\n* @example\n* rgb2contrast([100, 0, 0], [0, 0, 0]) // => 5.252\n* @link https://www.w3.org/TR/WCAG21/#dfn-contrast-ratio\n*/\n\nexport default function rgb2contrast(rgb1, rgb2) {\n\tconst luminance1 = rgb2luminance(...rgb1);\n\tconst luminance2 = rgb2luminance(...rgb2);\n\n\treturn luminance2contrast(luminance1, luminance2);\n}\n\n/**\n* @private\n* @func rgb2luminance\n* @desc Return the relative brightness of RGB\n* @param {Number} r - Red (0 - 100)\n* @param {Number} g - Green (0 - 100)\n* @param {Number} b - Blue (0 - 100)\n* @return {Number} Relative luminance of the color\n* @example\n* rgb2luminance(100, 0, 0) // => 0.2126\n* @link https://www.w3.org/TR/WCAG21/#dfn-relative-luminance\n*/\n\nexport function rgb2luminance(rgbR, rgbG, rgbB) {\n\treturn (adjustChannel(rgbR) * coefficientR + adjustChannel(rgbG) * coefficientG + adjustChannel(rgbB) * coefficientB) / precision;\n}\n\n// low-gamma adjust coefficients\nconst adjustChannel = x => x <= 3.928 ? x / lowc : adjustGamma(x);\nconst adjustGamma = x => pow((x + 5.5) / 105.5, 2.4);\nconst lowc = 1292;\n\n// red/green/blue coefficients\nconst coefficientR = 0.2126 * precision;\nconst coefficientG = 0.7152 * precision;\nconst coefficientB = 0.0722 * precision;\n", "/**\n* @func hex2rgb\n* @desc Return an RGBA color from a Hex color.\n* @param {StringHex} hex\n* @return {ArrayRGBA}\n* @example\n* hex2rgb(\"#f00\") // => [100, 0, 0, 100]\n* hex2rgb(\"#f00f\") // => [100, 0, 0, 100]\n* @example\n* hex2rgb(\"#ff0000\") // => [100, 0, 0, 100]\n* hex2rgb(\"#ff0000ff\") // => [100, 0, 0, 100]\n*/\n\nexport function hex2rgb(hex) {\n\t// #<hex-color>{3,4,6,8}\n\tconst [, r, g, b, a, rr, gg, bb, aa] = hex.match(hexColorMatch) || [];\n\n\tif (rr !== undefined || r !== undefined) {\n\t\tconst red   = rr !== undefined ? parseInt(rr, 16) : parseInt(r + r, 16);\n\t\tconst green = gg !== undefined ? parseInt(gg, 16) : parseInt(g + g, 16);\n\t\tconst blue  = bb !== undefined ? parseInt(bb, 16) : parseInt(b + b, 16);\n\t\tconst alpha = aa !== undefined ? parseInt(aa, 16) : a !== undefined ? parseInt(a + a, 16) : 255;\n\n\t\treturn [red, green, blue, alpha].map(c => c * 100 / 255);\n\t}\n\n\treturn undefined;\n}\n\n/**\n* @func rgb2hex\n* @desc Return a HEX color from an RGB color\n* @param {Number} r - Red (0 - 100)\n* @param {Number} g - Green (0 - 100)\n* @param {Number} b - Blue (0 - 100)\n* @return {StringHex}\n* @example\n* rgb2hex(100, 0, 0) // => \"#ff0000\"\n*/\n\nexport function rgb2hex(rgbR, rgbG, rgbB) {\n\treturn `#${((1 << 24) + (Math.round(rgbR * 255 / 100) << 16) + (Math.round(rgbG * 255 / 100) << 8) + Math.round(rgbB * 255 / 100)).toString(16).slice(1)}`;\n}\n\nconst hexColorMatch = /^#?(?:([a-f0-9])([a-f0-9])([a-f0-9])([a-f0-9])?|([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})?)$/i;\n", "/**\n* @func keyword2rgb\n* @desc Return an RGB color from a CSS keyword color\n* @param {StringKeyword} keyword\n* @return {ArrayRGB}\n* @example\n* keyword2rgb('red') // => [100, 0, 0]\n*/\n\nexport default function keyword2rgb(keyword) {\n\tconst rgb255 = keywords[String(keyword).toLowerCase()];\n\n\treturn rgb255 ? rgb255.map(x => x * 100 / 255) : null;\n}\n\nconst keywords = {\n\taliceblue: [240, 248, 255],\n\tantiquewhite: [250, 235, 215],\n\taqua: [0, 255, 255],\n\taquamarine: [127, 255, 212],\n\tazure: [240, 255, 255],\n\tbeige: [245, 245, 220],\n\tbisque: [255, 228, 196],\n\tblack: [0, 0, 0],\n\tblanchedalmond: [255, 235, 205],\n\tblue: [0, 0, 255],\n\tblueviolet: [138, 43, 226],\n\tbrown: [165, 42, 42],\n\tburlywood: [222, 184, 135],\n\tcadetblue: [95, 158, 160],\n\tchartreuse: [127, 255, 0],\n\tchocolate: [210, 105, 30],\n\tcoral: [255, 127, 80],\n\tcornflowerblue: [100, 149, 237],\n\tcornsilk: [255, 248, 220],\n\tcrimson: [220, 20, 60],\n\tcyan: [0, 255, 255],\n\tdarkblue: [0, 0, 139],\n\tdarkcyan: [0, 139, 139],\n\tdarkgoldenrod: [184, 134, 11],\n\tdarkgray: [169, 169, 169],\n\tdarkgreen: [0, 100, 0],\n\tdarkgrey: [169, 169, 169],\n\tdarkkhaki: [189, 183, 107],\n\tdarkmagenta: [139, 0, 139],\n\tdarkolivegreen: [85, 107, 47],\n\tdarkorange: [255, 140, 0],\n\tdarkorchid: [153, 50, 204],\n\tdarkred: [139, 0, 0],\n\tdarksalmon: [233, 150, 122],\n\tdarkseagreen: [143, 188, 143],\n\tdarkslateblue: [72, 61, 139],\n\tdarkslategray: [47, 79, 79],\n\tdarkslategrey: [47, 79, 79],\n\tdarkturquoise: [0, 206, 209],\n\tdarkviolet: [148, 0, 211],\n\tdeeppink: [255, 20, 147],\n\tdeepskyblue: [0, 191, 255],\n\tdimgray: [105, 105, 105],\n\tdimgrey: [105, 105, 105],\n\tdodgerblue: [30, 144, 255],\n\tfirebrick: [178, 34, 34],\n\tfloralwhite: [255, 250, 240],\n\tforestgreen: [34, 139, 34],\n\tfuchsia: [255, 0, 255],\n\tgainsboro: [220, 220, 220],\n\tghostwhite: [248, 248, 255],\n\tgold: [255, 215, 0],\n\tgoldenrod: [218, 165, 32],\n\tgray: [128, 128, 128],\n\tgreen: [0, 128, 0],\n\tgreenyellow: [173, 255, 47],\n\tgrey: [128, 128, 128],\n\thoneydew: [240, 255, 240],\n\thotpink: [255, 105, 180],\n\tindianred: [205, 92, 92],\n\tindigo: [75, 0, 130],\n\tivory: [255, 255, 240],\n\tkhaki: [240, 230, 140],\n\tlavender: [230, 230, 250],\n\tlavenderblush: [255, 240, 245],\n\tlawngreen: [124, 252, 0],\n\tlemonchiffon: [255, 250, 205],\n\tlightblue: [173, 216, 230],\n\tlightcoral: [240, 128, 128],\n\tlightcyan: [224, 255, 255],\n\tlightgoldenrodyellow: [250, 250, 210],\n\tlightgray: [211, 211, 211],\n\tlightgreen: [144, 238, 144],\n\tlightgrey: [211, 211, 211],\n\tlightpink: [255, 182, 193],\n\tlightsalmon: [255, 160, 122],\n\tlightseagreen: [32, 178, 170],\n\tlightskyblue: [135, 206, 250],\n\tlightslategray: [119, 136, 153],\n\tlightslategrey: [119, 136, 153],\n\tlightsteelblue: [176, 196, 222],\n\tlightyellow: [255, 255, 224],\n\tlime: [0, 255, 0],\n\tlimegreen: [50, 205, 50],\n\tlinen: [250, 240, 230],\n\tmagenta: [255, 0, 255],\n\tmaroon: [128, 0, 0],\n\tmediumaquamarine: [102, 205, 170],\n\tmediumblue: [0, 0, 205],\n\tmediumorchid: [186, 85, 211],\n\tmediumpurple: [147, 112, 219],\n\tmediumseagreen: [60, 179, 113],\n\tmediumslateblue: [123, 104, 238],\n\tmediumspringgreen: [0, 250, 154],\n\tmediumturquoise: [72, 209, 204],\n\tmediumvioletred: [199, 21, 133],\n\tmidnightblue: [25, 25, 112],\n\tmintcream: [245, 255, 250],\n\tmistyrose: [255, 228, 225],\n\tmoccasin: [255, 228, 181],\n\tnavajowhite: [255, 222, 173],\n\tnavy: [0, 0, 128],\n\toldlace: [253, 245, 230],\n\tolive: [128, 128, 0],\n\tolivedrab: [107, 142, 35],\n\torange: [255, 165, 0],\n\torangered: [255, 69, 0],\n\torchid: [218, 112, 214],\n\tpalegoldenrod: [238, 232, 170],\n\tpalegreen: [152, 251, 152],\n\tpaleturquoise: [175, 238, 238],\n\tpalevioletred: [219, 112, 147],\n\tpapayawhip: [255, 239, 213],\n\tpeachpuff: [255, 218, 185],\n\tperu: [205, 133, 63],\n\tpink: [255, 192, 203],\n\tplum: [221, 160, 221],\n\tpowderblue: [176, 224, 230],\n\tpurple: [128, 0, 128],\n\trebeccapurple: [102, 51, 153],\n\tred: [255, 0, 0],\n\trosybrown: [188, 143, 143],\n\troyalblue: [65, 105, 225],\n\tsaddlebrown: [139, 69, 19],\n\tsalmon: [250, 128, 114],\n\tsandybrown: [244, 164, 96],\n\tseagreen: [46, 139, 87],\n\tseashell: [255, 245, 238],\n\tsienna: [160, 82, 45],\n\tsilver: [192, 192, 192],\n\tskyblue: [135, 206, 235],\n\tslateblue: [106, 90, 205],\n\tslategray: [112, 128, 144],\n\tslategrey: [112, 128, 144],\n\tsnow: [255, 250, 250],\n\tspringgreen: [0, 255, 127],\n\tsteelblue: [70, 130, 180],\n\ttan: [210, 180, 140],\n\tteal: [0, 128, 128],\n\tthistle: [216, 191, 216],\n\ttomato: [255, 99, 71],\n\ttransparent: [0, 0, 0],\n\tturquoise: [64, 224, 208],\n\tviolet: [238, 130, 238],\n\twheat: [245, 222, 179],\n\twhite: [255, 255, 255],\n\twhitesmoke: [245, 245, 245],\n\tyellow: [255, 255, 0],\n\tyellowgreen: [154, 205, 50]\n};\n", "import { abs, atan2d, cosd, exp, pow, sind, sqrt, precision } from './util';\n\n/* Return\n/* ========================================================================== */\n\n/**\n* @func lab2ciede\n* @desc Return the CIEDE2000 difference between 2 CIE LAB colors (International Commission on Illumination, Delta E).\n* @param {Array} lab1 - CIE LAB color\n* @param {Number} lab1.0 - Lightness\n* @param {Number} lab1.1 - Red/Green Coordinate\n* @param {Number} lab1.2 - Yellow/Blue Coordinate\n* @param {Array} lab2 - CIE LAB color\n* @param {Number} lab2.0 - Lightness\n* @param {Number} lab2.1 - Red/Green Coordinate\n* @param {Number} lab2.2 - Yellow/Blue Coordinate\n* @return {NumberCIEDE}\n* @example\n* lab2ciede([97.14, -21.56, 94.48], [0, 0, 0]) // => 100\n* @link https://en.wikipedia.org/wiki/Color_difference#CIEDE2000\n*/\n\nexport default function lab2ciede([L1, a1, b1], [L2, a2, b2]) {\n\tconst c1 = sqrt(pow(a1, 2) + pow(b1, 2));\n\tconst c2 = sqrt(pow(a2, 2) + pow(b2, 2));\n\n\tconst deltaLPrime = L2 - L1;\n\n\tconst lBar = (L1 + L2) / 2;\n\tconst cBar = (c1 + c2) / 2;\n\n\tconst cBarPow7 = pow(cBar, 7);\n\tconst cCoeff = sqrt(cBarPow7 / (cBarPow7 + pow(25, 7)));\n\tconst a1Prime = a1 + a1 / 2 * (1 - cCoeff);\n\tconst a2Prime = a2 + a2 / 2 * (1 - cCoeff);\n\n\tconst c1Prime = sqrt(a1Prime * a1Prime + b1 * b1);\n\tconst c2Prime = sqrt(a2Prime * a2Prime + b2 * b2);\n\tconst cBarPrime = (c1Prime + c2Prime) / 2;\n\tconst deltaCPrime = c2Prime - c1Prime;\n\n\tconst h1Prime = a1Prime === 0 && b1 === 0 ? 0 : atan2d(b1, a1Prime) % 360;\n\tconst h2Prime = a2Prime === 0 && b2 === 0 ? 0 : atan2d(b2, a2Prime) % 360;\n\n\tlet deltaSmallHPrime;\n\tlet deltaBigHPrime;\n\tlet hBarPrime;\n\n\tif (c1Prime === 0 || c2Prime === 0) {\n\t\tdeltaSmallHPrime = 0;\n\t\tdeltaBigHPrime = 0;\n\t\thBarPrime = h1Prime + h2Prime;\n\t} else {\n\t\tdeltaSmallHPrime = abs(h1Prime - h2Prime) <= 180\n\t\t\t? h2Prime - h1Prime\n\t\t: h2Prime <= h1Prime\n\t\t\t? h2Prime - h1Prime + 360\n\t\t: h2Prime - h1Prime - 360;\n\n\t\tdeltaBigHPrime = 2 * sqrt(c1Prime * c2Prime) * sind(deltaSmallHPrime / 2);\n\n\t\thBarPrime = abs(h1Prime - h2Prime) <= 180\n\t\t\t? (h1Prime + h2Prime) / 2\n\t\t: h1Prime + h2Prime < 360\n\t\t\t? (h1Prime + h2Prime + 360) / 2\n\t\t: (h1Prime + h2Prime - 360) / 2;\n\t}\n\n\tconst T = 1 - 0.17 * precision * cosd(hBarPrime - 30) + 0.24 * precision * cosd(2 * hBarPrime) + 0.32 * precision * cosd(3 * hBarPrime + 6) - 0.2 * precision * cosd(4 * hBarPrime - 63) / precision / precision;\n\n\tconst slCoeff = (lBar - 50) * (lBar - 50);\n\tconst sl = 1 + 0.015 * precision * slCoeff / sqrt(20 + slCoeff) / precision;\n\tconst sc = 1 + 0.045 * precision * cBarPrime / precision;\n\tconst sh = 1 + 0.015 * precision * cBarPrime * T / precision;\n\n\tconst RtCoeff = 60 * exp(-((hBarPrime - 275) / 25) * ((hBarPrime - 275) / 25));\n\tconst Rt = -2 * cCoeff * sind(RtCoeff);\n\n\tconst term1 = deltaLPrime / (kl * sl);\n\tconst term2 = deltaCPrime / (kc * sc);\n\tconst term3 = deltaBigHPrime / (kh * sh);\n\tconst term4 = Rt * term2 * term3;\n\n\treturn sqrt(term1 * term1 + term2 * term2 + term3 * term3 + term4);\n}\n\n// weight factors\nconst kl = 1;\nconst kc = 1;\nconst kh = 1;\n", "import { rgb2hsl, hsl2rgb } from './rgb-hsl';\nimport { rgb2hwb, hwb2rgb } from './rgb-hwb';\nimport { rgb2hsv, hsv2rgb } from './rgb-hsv';\nimport { rgb2xyz, xyz2rgb } from './rgb-xyz';\nimport { hsl2hsv, hsv2hsl } from './hsl-hsv';\nimport { hwb2hsv, hsv2hwb } from './hwb-hsv';\nimport { lab2xyz, xyz2lab } from './lab-xyz';\nimport { lab2lch, lch2lab } from './lab-lch';\nimport rgb2contrast from './rgb-contrast';\nimport { hex2rgb, rgb2hex } from './hex-rgb';\nimport keyword2rgb from './keyword-rgb';\nimport lab2ciede from './lab-ciede';\n\n/* Convert between RGB and Lab\n/* ========================================================================== */\n\n/**\n* @func rgb2lab\n* @desc Return a CIE LAB color from an RGB color\n* @param {Number} r - Red (0 - 100)\n* @param {Number} g - Green (0 - 100)\n* @param {Number} b - Blue (0 - 100)\n* @return {ArrayLAB}\n* @example\n* rgb2lab(100, 0, 0) // => [54.29, 80.82, 69.88]\n*/\n\nfunction rgb2lab(rgbR, rgbG, rgbB) {\n\tconst [ xyzX, xyzY, xyzZ ] = rgb2xyz(rgbR, rgbG, rgbB);\n\tconst [ labL, labA, labB ] = xyz2lab(xyzX, xyzY, xyzZ);\n\n\treturn [ labL, labA, labB ];\n}\n\n/**\n* @func lab2rgb\n* @desc Return an RGB color from a CIE LAB color\n* @param {Number} l - CIE Lightness\n* @param {Number} a - Red/Green Coordinate\n* @param {Number} b - Yellow/Blue Coordinate\n* @return {ArrayRGBA}\n* @example\n* lab2rgb(54.29, 80.82, 69.88) // => [100, 0, 0]\n*/\n\nfunction lab2rgb(labL, labA, labB) {\n\tconst [ xyzX, xyzY, xyzZ ] = lab2xyz(labL, labA, labB);\n\tconst [ rgbR, rgbG, rgbB ] = xyz2rgb(xyzX, xyzY, xyzZ);\n\n\treturn [ rgbR, rgbG, rgbB ];\n}\n\n/* Convert between RGB and LCH\n/* ========================================================================== */\n\n/**\n* @func rgb2lch\n* @desc Return a CIE LAB color from an RGB color\n* @param {Number} r - Red (0 - 100)\n* @param {Number} g - Green (0 - 100)\n* @param {Number} b - Blue (0 - 100)\n* @return {ArrayLCH}\n* @example\n* rgb2lch(100, 0, 0) // => [54.29, 106.84, 40.85]\n*/\n\nfunction rgb2lch(rgbR, rgbG, rgbB) {\n\tconst [ xyzX, xyzY, xyzZ ] = rgb2xyz(rgbR, rgbG, rgbB);\n\tconst [ labL, labA, labB ] = xyz2lab(xyzX, xyzY, xyzZ);\n\tconst [ lchL, lchC, lchH ] = lab2lch(labL, labA, labB);\n\n\treturn [ lchL, lchC, lchH ];\n}\n\n/**\n* @func lch2rgb\n* @desc Return an RGB color from a CIE LCH color\n* @param {Number} l - CIE Lightness\n* @param {Number} c - CIE Chroma\n* @param {Number} h - CIE Hue\n* @return {ArrayRGBA}\n* @example\n* lch2rgb(54.29, 106.84, 40.85) // => [100, 0, 0]\n*/\n\nfunction lch2rgb(lchL, lchC, lchH) {\n\tconst [ labL, labA, labB ] = lch2lab(lchL, lchC, lchH);\n\tconst [ xyzX, xyzY, xyzZ ] = lab2xyz(labL, labA, labB);\n\tconst [ rgbR, rgbG, rgbB ] = xyz2rgb(xyzX, xyzY, xyzZ);\n\n\treturn [ rgbR, rgbG, rgbB ];\n}\n\n/* Convert between HSL and HWB\n/* ========================================================================== */\n\n/**\n* @func hwb2hsl\n* @desc Return an HSV color from an HWB color\n* @param {Number} h - Hue Angle (0 - 360)\n* @param {Number} w - Whiteness (0 - 100)\n* @param {Number} b - Blackness (0 - 100)\n* @return {ArrayHSL}\n* @example\n* hwb2hsl(0, 0, 0) // => [0, 0, 100]\n*/\n\nfunction hwb2hsl(hwbH, hwbW, hwbB) {\n\tconst [ hsvH, hsvS, hsvV ] = hwb2hsv(hwbH, hwbW, hwbB);\n\tconst [ hslH, hslS, hslL ] = hsv2hsl(hsvH, hsvS, hsvV);\n\n\treturn [ hslH, hslS, hslL ];\n}\n\n/**\n* @func hsl2hwb\n* @desc Return an HWB color from an HSL color\n* @param {Number} h - Hue Angle (0 - 360)\n* @param {Number} s - Saturation (0 - 100)\n* @param {Number} l - Lightness (0 - 100)\n* @return {ArrayHWB}\n* @example\n* hsl2hwb(0, 0, 100) // => [0, 0, 0]\n*/\n\nfunction hsl2hwb(hslH, hslS, hslL) {\n\tconst [ , hsvS, hsvV ] = hsl2hsv(hslH, hslS, hslL);\n\tconst [ , hwbW, hwbB ] = hsv2hwb(hslH, hsvS, hsvV);\n\n\treturn [ hslH, hwbW, hwbB ];\n}\n\n/* Convert between HSL and Lab\n/* ========================================================================== */\n\n/**\n* @func hsl2lab\n* @desc Return a CIE LAB color from an HSL color\n* @param {Number} h - Hue Angle (0 - 360)\n* @param {Number} s - Saturation (0 - 100)\n* @param {Number} l - Lightness (0 - 100)\n* @return {ArrayLAB}\n* @example\n* hsl2lab(0, 100, 50) // => [54.29, 80.82, 69.88]\n*/\n\nfunction hsl2lab(hslH, hslS, hslL) {\n\tconst [ rgbR, rgbG, rgbB ] = hsl2rgb(hslH, hslS, hslL);\n\tconst [ xyzX, xyzY, xyzZ ] = rgb2xyz(rgbR, rgbG, rgbB);\n\tconst [ labL, labA, labB ] = xyz2lab(xyzX, xyzY, xyzZ);\n\n\treturn [ labL, labA, labB ];\n}\n\n/**\n* @func lab2hsl\n* @desc Return a HSL color from a CIE LAB color\n* @param {Number} l - CIE Lightness\n* @param {Number} a - Red/Green Coordinate\n* @param {Number} b - Yellow/Blue Coordinate\n* @param {Number=} f - Hue Fallback (0 - 360)\n* @return {ArrayHSL}\n* @example\n* lab2hsl(54.29, 80.82, 69.88) // => [0, 100, 50]\n*/\n\nfunction lab2hsl(labL, labA, labB, fallbackhue) {\n\tconst [ xyzX, xyzY, xyzZ ] = lab2xyz(labL, labA, labB);\n\tconst [ rgbR, rgbG, rgbB ] = xyz2rgb(xyzX, xyzY, xyzZ);\n\tconst [ hslH, hslS, hslL ] = rgb2hsl(rgbR, rgbG, rgbB, fallbackhue);\n\n\treturn [ hslH, hslS, hslL ];\n}\n\n/* Convert between HSL and LCH\n/* ========================================================================== */\n\n/**\n* @func hsl2lch\n* @desc Return a CIE LCH color from an HSL color\n* @param {Number} h - Hue Angle (0 - 360)\n* @param {Number} s - Saturation (0 - 100)\n* @param {Number} l - Lightness (0 - 100)\n* @return {ArrayLCH}\n* @example\n* hsl2lch(0, 100, 50) // => [54.29, 106.84, 40.85]\n*/\n\nfunction hsl2lch(hslH, hslS, hslL) {\n\tconst [ rgbR, rgbG, rgbB ] = hsl2rgb(hslH, hslS, hslL);\n\tconst [ xyzX, xyzY, xyzZ ] = rgb2xyz(rgbR, rgbG, rgbB);\n\tconst [ labL, labA, labB ] = xyz2lab(xyzX, xyzY, xyzZ);\n\tconst [ lchL, lchC, lchH ] = lab2lch(labL, labA, labB);\n\n\treturn [ lchL, lchC, lchH ];\n}\n\n/**\n* @func lch2hsl\n* @desc Return an HSL from a CIE LCH color\n* @param {Number} l - CIE Lightness\n* @param {Number} c - CIE Chroma\n* @param {Number} h - CIE Hue Angle\n* @return {ArrayLCH}\n* @example\n* lch2hsl(54.29, 106.84, 40.85) // => [0, 100, 50]\n*/\n\nfunction lch2hsl(lchL, lchC, lchH, fallbackhue) {\n\tconst [ labL, labA, labB ] = lch2lab(lchL, lchC, lchH);\n\tconst [ xyzX, xyzY, xyzZ ] = lab2xyz(labL, labA, labB);\n\tconst [ rgbR, rgbG, rgbB ] = xyz2rgb(xyzX, xyzY, xyzZ);\n\tconst [ hslH, hslS, hslL ] = rgb2hsl(rgbR, rgbG, rgbB, fallbackhue);\n\n\treturn [ hslH, hslS, hslL ];\n}\n\n/* Convert between HSL and XYZ\n/* ========================================================================== */\n\n/**\n* @func hsl2xyz\n* @desc Return an XYZ color from an HSL color\n* @param {Number} h - Hue Angle (0 - 360)\n* @param {Number} s - Saturation (0 - 100)\n* @param {Number} l - Lightness (0 - 100)\n* @return {ArrayXYZ}\n* @example\n* hsl2xyz(0, 100, 50) // => [41.25, 21.27, 1.93]\n*/\n\nfunction hsl2xyz(hslH, hslS, hslL) {\n\tconst [ rgbR, rgbG, rgbB ] = hsl2rgb(hslH, hslS, hslL);\n\tconst [ xyzX, xyzY, xyzZ ] = rgb2xyz(rgbR, rgbG, rgbB);\n\n\treturn [ xyzX, xyzY, xyzZ ];\n}\n\n/**\n* @func xyz2hsl\n* @desc Return an HSL color from an XYZ color\n* @param {Number} x - Chromaticity of X\n* @param {Number} y - Chromaticity of Y\n* @param {Number} z - Chromaticity of Z\n* @return {ArrayHSL}\n* @example\n* xyz2hsl(0, 100, 50) // => [41.25, 21.27, 1.93]\n*/\n\nfunction xyz2hsl(xyzX, xyzY, xyzZ, fallbackhue) {\n\tconst [ rgbR, rgbG, rgbB ] = xyz2rgb(xyzX, xyzY, xyzZ);\n\tconst [ hslH, hslS, hslL ] = rgb2hsl(rgbR, rgbG, rgbB, fallbackhue);\n\n\treturn [ hslH, hslS, hslL ];\n}\n\n/* Convert between HWB and Lab\n/* ========================================================================== */\n\n/**\n* @func hwb2lab\n* @desc Return a CIE LAB color from an HWB color\n* @param {Number} h - Hue Angle (0 - 360)\n* @param {Number} w - Whiteness (0 - 100)\n* @param {Number} b - Blackness (0 - 100)\n* @return {ArrayLAB}\n* @example\n* hwb2lab(0, 0, 0) // => [54.29, 80.82, 69.88]\n*/\n\nfunction hwb2lab(hwbH, hwbW, hwbB) {\n\tconst [ rgbR, rgbG, rgbB ] = hwb2rgb(hwbH, hwbW, hwbB);\n\tconst [ xyzX, xyzY, xyzZ ] = rgb2xyz(rgbR, rgbG, rgbB);\n\tconst [ labL, labA, labB ] = xyz2lab(xyzX, xyzY, xyzZ);\n\n\treturn [ labL, labA, labB ];\n}\n\n/**\n* @func lab2hwb\n* @desc Return an HWB color from a CIE LAB color\n* @param {Number} l - CIE Lightness\n* @param {Number} a - Red/Green Coordinate\n* @param {Number} b - Yellow/Blue Coordinate\n* @return {ArrayHWB}\n* @example\n* lab2hwb(54.29, 80.82, 69.88) // => [0, 0, 0]\n*/\n\nfunction lab2hwb(labL, labA, labB, fallbackhue) {\n\tconst [ xyzX, xyzY, xyzZ ] = lab2xyz(labL, labA, labB);\n\tconst [ rgbR, rgbG, rgbB ] = xyz2rgb(xyzX, xyzY, xyzZ);\n\tconst [ hwbH, hwbW, hwbB ] = rgb2hwb(rgbR, rgbG, rgbB, fallbackhue);\n\n\treturn [ hwbH, hwbW, hwbB ];\n}\n\n/* Convert between HWB and LCH\n/* ========================================================================== */\n\n/**\n* @func hwb2lch\n* @desc Return a CIE LCH color from an HWB color\n* @param {Number} h - Hue Angle (0 - 360)\n* @param {Number} w - Whiteness (0 - 100)\n* @param {Number} b - Blackness (0 - 100)\n* @return {ArrayLCH}\n* @example\n* hwb2lch(0, 0, 0) // => [54.29, 106.84, 40.85]\n*/\n\nfunction hwb2lch(hwbH, hwbW, hwbB) {\n\tconst [ rgbR, rgbG, rgbB ] = hwb2rgb(hwbH, hwbW, hwbB);\n\tconst [ xyzX, xyzY, xyzZ ] = rgb2xyz(rgbR, rgbG, rgbB);\n\tconst [ labL, labA, labB ] = xyz2lab(xyzX, xyzY, xyzZ);\n\tconst [ lchL, lchC, lchH ] = lab2lch(labL, labA, labB);\n\n\treturn [ lchL, lchC, lchH ];\n}\n\n/**\n* @func lch2hwb\n* @desc Return an HWB color from a CIE LCH color\n* @param {Number} l - CIE Lightness\n* @param {Number} c - CIE Chroma\n* @param {Number} h - CIE Hue Angle\n* @return {ArrayLCH}\n* @example\n* lch2hwb(54.29, 106.84, 40.85) // => [0, 0, 0]\n*/\n\nfunction lch2hwb(lchL, lchC, lchH, fallbackhue) {\n\tconst [ labL, labA, labB ] = lch2lab(lchL, lchC, lchH);\n\tconst [ xyzX, xyzY, xyzZ ] = lab2xyz(labL, labA, labB);\n\tconst [ rgbR, rgbG, rgbB ] = xyz2rgb(xyzX, xyzY, xyzZ);\n\tconst [ hwbH, hwbW, hwbB ] = rgb2hwb(rgbR, rgbG, rgbB, fallbackhue);\n\n\treturn [ hwbH, hwbW, hwbB ];\n}\n\n/* Convert between HWB and XYZ\n/* ========================================================================== */\n\n/**\n* @func hwb2xyz\n* @desc Return an XYZ color from an HWB color\n* @param {Number} h - Hue Angle (0 - 360)\n* @param {Number} w - Whiteness (0 - 100)\n* @param {Number} b - Blackness (0 - 100)\n* @return {ArrayXYZ}\n* @example\n* hwb2xyz(0, 0, 0) // => [41.25, 21.27, 1.93]\n*/\n\nfunction hwb2xyz(hwbH, hwbW, hwbB) {\n\tconst [ rgbR, rgbG, rgbB ] = hwb2rgb(hwbH, hwbW, hwbB);\n\tconst [ xyzX, xyzY, xyzZ ] = rgb2xyz(rgbR, rgbG, rgbB);\n\n\treturn [ xyzX, xyzY, xyzZ ];\n}\n\n/**\n* @func xyz2hwb\n* @desc Return an HWB color from an XYZ color\n* @param {Number} x - Chromaticity of X\n* @param {Number} y - Chromaticity of Y\n* @param {Number} z - Chromaticity of Z\n* @return {ArrayXYZ}\n* @example\n* xyz2hwb(0, 0, 0) // => [41.25, 21.27, 1.93]\n*/\n\nfunction xyz2hwb(xyzX, xyzY, xyzZ, fallbackhue) {\n\tconst [ rgbR, rgbG, rgbB ] = xyz2rgb(xyzX, xyzY, xyzZ);\n\tconst [ hwbH, hwbW, hwbB ] = rgb2hwb(rgbR, rgbG, rgbB, fallbackhue);\n\n\treturn [ hwbH, hwbW, hwbB ];\n}\n\n/* Convert between HSV and Lab\n/* ========================================================================== */\n\n/**\n* @func hsv2lab\n* @desc Return a CIE LAB color from an HSV color\n* @param {Number} h - Hue Angle (0 - 360)\n* @param {Number} s - Saturation (0 - 100)\n* @param {Number} v - Value (0 - 100)\n* @return {ArrayLAB}\n* @example\n* hsv2lab(0, 100, 100) // => [54.29, 80.82, 69.88]\n*/\n\nfunction hsv2lab(hsvH, hsvS, hsvV) {\n\tconst [ rgbR, rgbG, rgbB ] = hsv2rgb(hsvH, hsvS, hsvV);\n\tconst [ xyzX, xyzY, xyzZ ] = rgb2xyz(rgbR, rgbG, rgbB);\n\tconst [ labL, labA, labB ] = xyz2lab(xyzX, xyzY, xyzZ);\n\n\treturn [ labL, labA, labB ];\n}\n\n/**\n* @func lab2hsv\n* @desc Return an HSV color from a CIE LAB color\n* @param {Number} l - CIE Lightness\n* @param {Number} a - Red/Green Coordinate\n* @param {Number} b - Yellow/Blue Coordinate\n* @return {ArrayHSV}\n* @example\n* lab2hsv(54.29, 80.82, 69.88) // => [0, 100, 100]\n*/\n\nfunction lab2hsv(labL, labA, labB, fallbackhue) {\n\tconst [ xyzX, xyzY, xyzZ ] = lab2xyz(labL, labA, labB);\n\tconst [ rgbR, rgbG, rgbB ] = xyz2rgb(xyzX, xyzY, xyzZ);\n\tconst [ hsvH, hsvS, hsvV ] = rgb2hsv(rgbR, rgbG, rgbB, fallbackhue);\n\n\treturn [ hsvH, hsvS, hsvV ];\n}\n\n/* Convert between HSV and LCH\n/* ========================================================================== */\n\n/**\n* @func hsv2lch\n* @desc Return a CIE LCH color from an HSV color\n* @param {Number} h - Hue Angle (0 - 360)\n* @param {Number} s - Saturation (0 - 100)\n* @param {Number} v - Value (0 - 100)\n* @return {ArrayLCH}\n* @example\n* hsv2lch(0, 100, 100) // => [54.29, 106.84, 40.85]\n*/\n\nfunction hsv2lch(hsvH, hsvS, hsvV) {\n\tconst [ rgbR, rgbG, rgbB ] = hsv2rgb(hsvH, hsvS, hsvV);\n\tconst [ xyzX, xyzY, xyzZ ] = rgb2xyz(rgbR, rgbG, rgbB);\n\tconst [ labL, labA, labB ] = xyz2lab(xyzX, xyzY, xyzZ);\n\tconst [ lchL, lchC, lchH ] = lab2lch(labL, labA, labB);\n\n\treturn [ lchL, lchC, lchH ];\n}\n\n/**\n* @func lch2hsv\n* @desc Return an HSV color from a CIE LCH color\n* @param {Number} l - CIE Lightness\n* @param {Number} c - CIE Chroma\n* @param {Number} h - CIE Hue Angle\n* @return {ArrayHSV}\n* @example\n* lch2hsv(54.29, 106.84, 40.85) // => [0, 100, 100]\n*/\n\nfunction lch2hsv(lchL, lchC, lchH, fallbackhue) {\n\tconst [ labL, labA, labB ] = lch2lab(lchL, lchC, lchH);\n\tconst [ xyzX, xyzY, xyzZ ] = lab2xyz(labL, labA, labB);\n\tconst [ rgbR, rgbG, rgbB ] = xyz2rgb(xyzX, xyzY, xyzZ);\n\tconst [ hsvH, hsvS, hsvV ] = rgb2hsv(rgbR, rgbG, rgbB, fallbackhue);\n\n\treturn [ hsvH, hsvS, hsvV ];\n}\n\n/* Convert between HSV and XYZ\n/* ========================================================================== */\n\n/**\n* @func hsv2xyz\n* @desc Return an XYZ color from an HSV color\n* @param {Number} h - Hue Angle (0 - 360)\n* @param {Number} s - Saturation (0 - 100)\n* @param {Number} v - Value (0 - 100)\n* @return {ArrayXYZ}\n* @example\n* hsv2xyz(0, 100, 100) // => [41.25, 21.27, 1.93]\n*/\n\nfunction hsv2xyz(hsvH, hsvS, hsvV) {\n\tconst [ rgbR, rgbG, rgbB ] = hsv2rgb(hsvH, hsvS, hsvV);\n\tconst [ xyzX, xyzY, xyzZ ] = rgb2xyz(rgbR, rgbG, rgbB);\n\n\treturn [ xyzX, xyzY, xyzZ ];\n}\n\n/**\n* @func xyz2hsv\n* @desc Return an XYZ color from an HSV color\n* @param {Number} x - Chromaticity of X\n* @param {Number} y - Chromaticity of Y\n* @param {Number} z - Chromaticity of Z\n* @return {ArrayHSV}\n* @example\n* xyz2hsv(41.25, 21.27, 1.93) // => [0, 100, 100]\n*/\n\nfunction xyz2hsv(xyzX, xyzY, xyzZ, fallbackhue) {\n\tconst [ rgbR, rgbG, rgbB ] = xyz2rgb(xyzX, xyzY, xyzZ);\n\tconst [ hsvH, hsvS, hsvV ] = rgb2hsv(rgbR, rgbG, rgbB, fallbackhue);\n\n\treturn [ hsvH, hsvS, hsvV ];\n}\n\n/* Convert between XYZ and LCH\n/* ========================================================================== */\n\n/**\n* @func xyz2lch\n* @desc Return a CIE LCH color from an XYZ color\n* @param {Number} x - Chromaticity of X\n* @param {Number} y - Chromaticity of Y\n* @param {Number} z - Chromaticity of Z\n* @return {ArrayLCH}\n* @example\n* xyz2lch(41.25, 21.27, 1.93) // => [54.29, 106.84, 40.85]\n*/\n\nfunction xyz2lch(xyzX, xyzY, xyzZ) {\n\tconst [ labL, labA, labB ] = xyz2lab(xyzX, xyzY, xyzZ);\n\tconst [ lchL, lchC, lchH ] = lab2lch(labL, labA, labB);\n\n\treturn [ lchL, lchC, lchH ];\n}\n\n/**\n* @func lch2xyz\n* @desc Return an XYZ color from a CIE LCH color\n* @param {Number} l - CIE Lightness\n* @param {Number} c - CIE Chroma\n* @param {Number} h - CIE Hue Angle\n* @return {ArrayXYZ}\n* @example\n* lch2xyz(54.29, 106.84, 40.85) // => [41.25, 21.27, 1.93]\n*/\n\nfunction lch2xyz(lchL, lchC, lchH) {\n\tconst [ labL, labA, labB ] = lch2lab(lchL, lchC, lchH);\n\tconst [ xyzX, xyzY, xyzZ ] = lab2xyz(labL, labA, labB);\n\n\treturn [ xyzX, xyzY, xyzZ ];\n}\n\n/* Hex input conversions\n/* ========================================================================== */\n\n/**\n* @func hex2hsl\n* @desc Return an HSL color from a Hex color\n* @param {StringHex} hex\n* @return {ArrayHSL}\n* @example\n* hex2hsl(\"#f00\") // => [0, 100, 50]\n*/\n\nfunction hex2hsl(hex) {\n\treturn rgb2hsl(...hex2rgb(hex));\n}\n\n/**\n* @func hex2hsv\n* @desc Return an HSL color from a Hex color\n* @param {StringHex} hex\n* @return {ArrayHSV}\n* @example\n* hex2hsv(\"#f00\") // => [0, 100, 100]\n*/\n\nfunction hex2hsv(hex) {\n\treturn rgb2hsv(...hex2rgb(hex));\n}\n\n/**\n* @func hex2hwb\n* @desc Return an HWB color from a Hex color\n* @param {StringHex} hex\n* @return {ArrayHWB}\n* @example\n* hex2hwb(\"#f00\") // => [0, 0, 0]\n*/\n\nfunction hex2hwb(hex) {\n\treturn rgb2hwb(...hex2rgb(hex));\n}\n\n/**\n* @func hex2lab\n* @desc Return a CIE LAB color from a Hex color\n* @param {StringHex} hex\n* @return {ArrayLAB}\n* @example\n* hex2lab(\"#f00\") // => [54.29, 80.82, 69.88]\n*/\n\nfunction hex2lab(hex) {\n\treturn rgb2lab(...hex2rgb(hex));\n}\n\n/**\n* @func hex2lch\n* @desc Return a CIE LCH color from a Hex color\n* @param {StringHex} hex\n* @return {ArrayLCH}\n* @example\n* hex2lch(\"#f00\") // => [54.29, 106.84, 40.85]\n*/\n\nfunction hex2lch(hex) {\n\treturn rgb2lch(...hex2rgb(hex));\n}\n\n/**\n* @func hex2xyz\n* @desc Return an XYZ color from a Hex color\n* @param {StringHex} hex\n* @return {ArrayXYZ}\n* @example\n* hex2xyz(\"#f00\") // => [41.25, 21.27, 1.93]\n*/\n\nfunction hex2xyz(hex) {\n\treturn rgb2xyz(...hex2rgb(hex));\n}\n\n/* Hex output conversions\n/* ========================================================================== */\n\n/**\n* @func hsl2hex\n* @desc Return a Hex color from an HSL color\n* @param {Number} h - Hue Angle (0 - 360)\n* @param {Number} s - Saturation (0 - 100)\n* @param {Number} l - Lightness (0 - 100)\n* @return {StringHex}\n* @example\n* hsl2hex(0, 100, 50) // => \"#f00\"\n*/\n\nfunction hsl2hex(hslH, hslS, hslL) {\n\treturn rgb2hex(...hsl2rgb(hslH, hslS, hslL));\n}\n\n/**\n* @func hsv2hex\n* @desc Return a Hex color from an HSV color\n* @param {Number} h - Hue Angle (0 - 360)\n* @param {Number} s - Saturation (0 - 100)\n* @param {Number} v - Value (0 - 100)\n* @return {StringHex}\n* @example\n* hsv2hex(0, 100, 100) // => \"#f00\"\n*/\n\nfunction hsv2hex(hsvH, hsvS, hsvV) {\n\treturn rgb2hex(...hsl2rgb(hsvH, hsvS, hsvV));\n}\n\n/**\n* @func hwb2hex\n* @desc Return a Hex color from an HWB color\n* @param {Number} h - Hue Angle (0 - 360)\n* @param {Number} w - Whiteness (0 - 100)\n* @param {Number} b - Blackness (0 - 100)\n* @return {StringHex}\n* @example\n* hwb2hex(0, 0, 0) // => \"#f00\"\n*/\n\nfunction hwb2hex(hwbH, hwbW, hwbB) {\n\treturn rgb2hex(...hwb2rgb(hwbH, hwbW, hwbB));\n}\n\n/**\n* @func lch2hex\n* @desc Return a Hex color from a CIE LAB color\n* @param {Number} l - CIE Lightness\n* @param {Number} a - Red/Green Coordinate\n* @param {Number} b - Yellow/Blue Coordinate\n* @return {StringHex}\n* @example\n* lch2hex(54.29, 80.82, 69.88) // => \"#f00\"\n*/\n\nfunction lab2hex(labL, labA, labB) {\n\treturn rgb2hex(...lab2rgb(labL, labA, labB));\n}\n\n/**\n* @func lch2hex\n* @desc Return a Hex color from a CIE LCH color\n* @param {Number} l - CIE Lightness\n* @param {Number} c - CIE Chroma\n* @param {Number} h - CIE Hue Angle\n* @return {StringHex}\n* @example\n* lch2hex(54.29, 106.84, 40.85) // => \"#f00\"\n*/\n\nfunction lch2hex(lchL, lchC, lchH) {\n\treturn rgb2hex(...lch2rgb(lchL, lchC, lchH));\n}\n\n/**\n* @func xyz2hex\n* @desc Return a Hex color from an XYZ color\n* @param {Number} x - Chromaticity of X\n* @param {Number} y - Chromaticity of Y\n* @param {Number} z - Chromaticity of Z\n* @return {StringHex}\n* @example\n* xyz2hex(41.25, 21.27, 1.93) // => \"#f00\"\n*/\n\nfunction xyz2hex(xyzX, xyzY, xyzZ) {\n\treturn rgb2hex(...xyz2rgb(xyzX, xyzY, xyzZ));\n}\n\n/* CIEDE conversions\n/* ========================================================================== */\n\n/**\n* @func hex2ciede\n* @desc Return the CIEDE2000 difference between 2 HEX colors\n* @param {StringHex} hex1\n* @param {StringHex} hex2\n* @return {NumberCIEDE}\n* @example\n* hex2ciede('#fff', '#000') // => 100\n*/\n\nfunction hex2ciede(hex1, hex2) {\n\treturn lab2ciede(hex2lab(hex1), hex2lab(hex2));\n}\n\n/**\n* @func hsl2ciede\n* @desc Return the CIEDE2000 difference between 2 HSL colors\n* @param {ArrayHSL} hsl1\n* @param {ArrayHSL} hsl2\n* @return {NumberCIEDE}\n* @example\n* hsl2ciede([0, 0, 100], [0, 0, 0]) // => 100\n*/\n\nfunction hsl2ciede(hsl1, hsl2) {\n\treturn lab2ciede(hsl2lab(...hsl1), hsl2lab(...hsl2));\n}\n\n/**\n* @func hsv2ciede\n* @desc Return the CIEDE2000 difference between 2 HSV colors\n* @param {ArrayHSV} hsl1\n* @param {ArrayHSV} hsl2\n* @return {NumberCIEDE}.\n* @example\n* hsv2ciede([0, 0, 40], [0, 0, 0]) // => 100\n*/\n\nfunction hsv2ciede(hsv1, hsv2) {\n\treturn lab2ciede(hsv2lab(...hsv1), hsv2lab(...hsv2));\n}\n\n/**\n* @func hwb2ciede\n* @desc Return the CIEDE2000 difference between 2 HWB colors\n* @param {ArrayHWB} hwb1\n* @param {ArrayHWB} hwb2\n* @return {NumberCIEDE}.\n* @example\n* hwb2ciede([0, 0, 40], [0, 0, 0]) // => 100\n*/\n\nfunction hwb2ciede(hwb1, hwb2) {\n\treturn lab2ciede(hwb2lab(...hwb1), hwb2lab(...hwb2));\n}\n\n/**\n* @func keyword2ciede\n* @desc Return the CIEDE2000 difference between 2 keyword colors\n* @param {StringKeyword} keyword1\n* @param {StringKeyword} keyword2\n* @return {NumberCIEDE}.\n* @example\n* keyword2ciede('white', 'black') // => 100\n*/\n\nfunction keyword2ciede(keyword1, keyword2) {\n\treturn lab2ciede(keyword2lab(keyword1), keyword2lab(keyword2));\n}\n\n/**\n* @func lch2ciede\n* @desc Return the CIEDE2000 difference between 2 LCH colors\n* @param {ArrayLCH} lch1\n* @param {ArrayLCH} lch2\n* @return {NumberCIEDE}.\n* @example\n* lch2ciede([100, 0.03, -82.2], [0, 0, 0]) // => 100\n*/\n\nfunction lch2ciede(lch1, lch2) {\n\treturn lab2ciede(lch2lab(...lch1), lch2lab(...lch2));\n}\n\n/**\n* @func rgb2ciede\n* @desc Return the CIEDE2000 difference between 2 RGB colors\n* @param {ArrayRGB} rgb1\n* @param {ArrayRGB} rgb2\n* @return {NumberCIEDE}.\n* @example\n* rgb2ciede([100, 100, 100], [0, 0, 0]) // => 100\n*/\n\nfunction rgb2ciede(rgb1, rgb2) {\n\treturn lab2ciede(rgb2lab(...rgb1), rgb2lab(...rgb2));\n}\n\n/**\n* @func xyz2ciede\n* @desc Return the CIEDE2000 difference between 2 XYZ colors\n* @param {ArrayXYZ} xyz1\n* @param {ArrayXYZ} xyz2\n* @return {NumberCIEDE}.\n* @example\n* xyz2ciede([95.05, 100, 108.88], [0, 0, 0]) // => 100\n*/\n\nfunction xyz2ciede(xyz1, xyz2) {\n\treturn lab2ciede(xyz2lab(...xyz1), xyz2lab(...xyz2));\n}\n\n/* Contrast conversions\n/* ========================================================================== */\n\n/**\n* @func hex2contrast\n* @desc Return the contrast ratio of 2 HEX colors\n* @param {StringHex} hex1\n* @param {StringHex} hex2\n* @return {NumberContrast}\n* @example\n* rgb2contrast(\"#fff\", '#000') // => 21\n*/\n\nfunction hex2contrast(hex1, hex2) {\n\treturn rgb2contrast(hex2rgb(hex1), hex2rgb(hex2));\n}\n\n/**\n* @func hsl2contrast\n* @desc Return the contrast ratio of 2 HSL colors\n* @param {ArrayHSL} hsl1\n* @param {ArrayHSL} hsl2\n* @return {NumberContrast}\n* @example\n* hsl2contrast([0, 0, 100], [0, 0, 0]) // => 21\n*/\n\nfunction hsl2contrast(hsl1, hsl2) {\n\treturn rgb2contrast(hsl2rgb(...hsl1), hsl2rgb(...hsl2));\n}\n\n/**\n* @func hsv2contrast\n* @desc Return the contrast ratio of 2 HSV colors\n* @param {ArrayHSV} hsv1\n* @param {ArrayHSV} hsv2\n* @return {NumberContrast}\n* @example\n* hsv2contrast([0, 0, 100], [0, 0, 0]) // => 21\n*/\n\nfunction hsv2contrast(hsv1, hsv2) {\n\treturn rgb2contrast(hsv2rgb(...hsv1), hsv2rgb(...hsv2));\n}\n\n/**\n* @func hwb2contrast\n* @desc Return the contrast ratio of 2 HWB colors\n* @param {ArrayHWB} hwb1\n* @param {ArrayHWB} hwb2\n* @return {NumberContrast}\n* @example\n* hwb2contrast([0, 100, 0], [0, 0, 100]) // => 21\n*/\n\nfunction hwb2contrast(hwb1, hwb2) {\n\treturn rgb2contrast(hwb2rgb(...hwb1), hwb2rgb(...hwb2));\n}\n\n/**\n* @func keyword2contrast\n* @desc Return the contrast ratio of 2 keyword colors\n* @param {StringKeyword} keyword1\n* @param {StringKeyword} keyword2\n* @return {NumberContrast}\n* @example\n* keyword2contrast('white', 'black') // => 21\n*/\n\nfunction keyword2contrast(keyword1, keyword2) {\n\treturn rgb2contrast(keyword2rgb(keyword1), keyword2rgb(keyword2));\n}\n\n/**\n* @func lab2contrast\n* @desc Return the contrast ratio of 2 LAB colors\n* @param {ArrayLAB} lab1\n* @param {ArrayLAB} lab2\n* @return {NumberContrast}\n* @example\n* lab2contrast([100, 0.003, -0.025], [0, 0, 0]) // => 21\n*/\n\nfunction lab2contrast(lab1, lab2) {\n\treturn rgb2contrast(lab2rgb(...lab1), lab2rgb(...lab2));\n}\n\n/**\n* @func lch2contrast\n* @desc Return the contrast ratio of 2 LCH colors\n* @param {ArrayLCH} lch1\n* @param {ArrayLCH} lch2\n* @return {NumberContrast}\n* @example\n* lch2contrast([100, 0.025, -82.2], [0, 0, 0]) // => 21\n*/\n\nfunction lch2contrast(lch1, lch2) {\n\treturn rgb2contrast(lch2rgb(...lch1), lch2rgb(...lch2));\n}\n\n/**\n* @func xyz2contrast\n* @desc Return the contrast ratio of 2 XYZ colors\n* @param {ArrayXYZ} xyz1\n* @param {ArrayXYZ} xyz2\n* @return {NumberContrast}\n* @example\n* xyz2contrast([95.05, 100, 108.88], [0, 0, 0]) // => 21\n*/\n\nfunction xyz2contrast(xyz1, xyz2) {\n\treturn rgb2contrast(xyz2rgb(...xyz1), xyz2rgb(...xyz2));\n}\n\n/* Keyword Conversions\n/* ========================================================================== */\n\n/**\n* @func keyword2hex\n* @desc Return an RGB color from a keyword color\n* @param {StringKeyword} keyword - CSS Color Keyword\n* @return {String}\n* @example\n* keyword2hex('white') // => \"#ffffff\"\n*/\n\nfunction keyword2hex(keyword) {\n\treturn rgb2hex(...keyword2rgb(keyword));\n}\n\n/**\n* @func keyword2hsl\n* @desc Return an HSL color from a keyword color\n* @param {StringKeyword}\n* @return {ArrayHSL}\n* @example\n* keyword2hsl('white') // => [0, 0, 100]\n*/\n\nfunction keyword2hsl(keyword) {\n\treturn rgb2hsl(...keyword2rgb(keyword));\n}\n\n/**\n* @func keyword2hsv\n* @desc Return an HSV color from a keyword color\n* @param {StringKeyword}\n* @return {ArrayHSV}\n* @example\n* keyword2hsv('white') // => [0, 0, 100]\n*/\n\nfunction keyword2hsv(keyword) {\n\treturn rgb2hsv(...keyword2rgb(keyword));\n}\n\n/**\n* @func keyword2hwb\n* @desc Return an HWB color from a keyword color\n* @param {StringKeyword}\n* @return {ArrayHWB}\n* @example\n* keyword2hwb('red') // => [0, 0, 0]\n*/\n\nfunction keyword2hwb(keyword) {\n\treturn rgb2hwb(...keyword2rgb(keyword));\n}\n\n/**\n* @func keyword2lab\n* @desc Return a CIE LAB color from a keyword color\n* @param {StringKeyword}\n* @return {ArrayLAB}\n* @example\n* keyword2lab('red') // => [54.29, 80.82, 69.88]\n*/\n\nfunction keyword2lab(keyword) {\n\treturn rgb2lab(...keyword2rgb(keyword));\n}\n\n/**\n* @func keyword2lch\n* @desc Return a CIE LCH color from a keyword color\n* @param {StringKeyword}\n* @return {ArrayLCH}\n* @example\n* keyword2lch('red') // => [54.29, 106.84, 40.85]\n*/\n\nfunction keyword2lch(keyword) {\n\treturn rgb2lch(...keyword2rgb(keyword));\n}\n\n/**\n* @func keyword2lch\n* @desc Return an XYZ color from a keyword color\n* @param {StringKeyword}\n* @return {ArrayXYZ}\n* @example\n* keyword2lch('red') // => [41.25, 21.27, 1.93]\n*/\n\nfunction keyword2xyz(keyword) {\n\treturn rgb2xyz(...keyword2rgb(keyword));\n}\n\n/* All Conversions\n/* ========================================================================== */\n\nexport {\n\thex2ciede,\n\thex2contrast,\n\thex2hsl,\n\thex2hsv,\n\thex2hwb,\n\thex2lab,\n\thex2lch,\n\thex2rgb,\n\thex2xyz,\n\n\thsl2ciede,\n\thsl2contrast,\n\thsl2hex,\n\thsl2hsv,\n\thsl2hwb,\n\thsl2lab,\n\thsl2lch,\n\thsl2rgb,\n\thsl2xyz,\n\n\thsv2ciede,\n\thsv2contrast,\n\thsv2hex,\n\thsv2hsl,\n\thsv2hwb,\n\thsv2lab,\n\thsv2lch,\n\thsv2rgb,\n\thsv2xyz,\n\n\thwb2ciede,\n\thwb2contrast,\n\thwb2hex,\n\thwb2hsl,\n\thwb2hsv,\n\thwb2lab,\n\thwb2lch,\n\thwb2rgb,\n\thwb2xyz,\n\n\tkeyword2ciede,\n\tkeyword2contrast,\n\tkeyword2hex,\n\tkeyword2hsl,\n\tkeyword2hsv,\n\tkeyword2hwb,\n\tkeyword2lab,\n\tkeyword2lch,\n\tkeyword2rgb,\n\tkeyword2xyz,\n\n\tlab2ciede,\n\tlab2contrast,\n\tlab2hex,\n\tlab2hsl,\n\tlab2hsv,\n\tlab2hwb,\n\tlab2lch,\n\tlab2rgb,\n\tlab2xyz,\n\n\tlch2ciede,\n\tlch2contrast,\n\tlch2hex,\n\tlch2hsl,\n\tlch2hsv,\n\tlch2hwb,\n\tlch2lab,\n\tlch2rgb,\n\tlch2xyz,\n\n\trgb2ciede,\n\trgb2contrast,\n\trgb2hex,\n\trgb2hsl,\n\trgb2hsv,\n\trgb2hwb,\n\trgb2lab,\n\trgb2lch,\n\trgb2xyz,\n\n\txyz2ciede,\n\txyz2contrast,\n\txyz2hex,\n\txyz2hsl,\n\txyz2hsv,\n\txyz2hwb,\n\txyz2lab,\n\txyz2lch,\n\txyz2rgb\n};\n\n/**\n* @typedef {Array} ArrayRGB\n* An array of red, green, and blue channels.\n* @property {Number} 0 - Red (0 - 100)\n* @property {Number} 1 - Green (0 - 100)\n* @property {Number} 2 - Blue (0 - 100)\n*\n*/\n/**\n* @typedef {Array} ArrayRGBA\n* An array of red, green, blue, and alpha channels.\n* @property {Number} 0 - Red (0 - 100)\n* @property {Number} 1 - Green (0 - 100)\n* @property {Number} 2 - Blue (0 - 100)\n* @property {Number} 3 - Alpha (0 - 100)\n*/\n/**\n* @typedef {Array} ArrayHSL\n* An array of hue, saturation, and lightness channels.\n* @property {Number} 0 - Hue Angle (0 - 360)\n* @property {Number} 1 - Saturation (0 - 100)\n* @property {Number} 2 - Lightness (0 - 100)\n*/\n/**\n* @typedef {Array} ArrayHSV\n* An array of hue, saturation, and value channels.\n* @property {Number} 0 - Hue Angle (0 - 360)\n* @property {Number} 1 - Saturation (0 - 100)\n* @property {Number} 2 - Value (0 - 100)\n*/\n/**\n* @typedef {Array} ArrayLAB\n* An array of CIELAB lightness, red/green, and yellow/blue.\n* @property {Number} 0 - CIE Lightness\n* @property {Number} 1 - Red/Green Coordinate\n* @property {Number} 2 - Yellow/Blue Coordinate\n*/\n/**\n* @typedef {Array} ArrayLCH\n* An array of CIELAB lightness, chroma, and hue.\n* @property {Number} 0 - CIE Lightness\n* @property {Number} 1 - CIE Chroma\n* @property {Number} 2 - CIE Hue\n*/\n/**\n* @typedef {Array} ArrayHWB\n* An array of hue, whiteness, and blackness channels.\n* @property {Number} 0 - Hue Angle (0 - 360)\n* @property {Number} 1 - Whiteness (0 - 100)\n* @property {Number} 2 - Blackness (0 - 100)\n*/\n/**\n* @typedef {Array} ArrayXYZ\n* An array of CIELAB chromacity.\n* @property {Number} 0 - X Chromacity\n* @property {Number} 1 - Y Chromacity\n* @property {Number} 2 - Z Chromacity\n* @example\n* [95.05, 100, 108.88]\n* @example\n* [0, 0, 0]\n*/\n/**\n* @typedef {String} StringHex\n* A string representing the 3, 4, 6, or 8 digit hexidecimal color.\n* @example\n* \"#f00\"\n* \"#f00f\"\n* @example\n* \"#ff0000\"\n* \"#ff0000ff\"\n*/\n/**\n* @typedef {String} StringKeyword\n* A case-insensitive string identifier that represents a specific color.\n* @example\n* \"#f00\"\n* \"#f00f\"\n* @example\n* \"#ff0000\"\n* \"#ff0000ff\"\n*/\n/**\n* @typedef {Number} NumberContrast\n* A contrast ratio of the colors (0 - 21)\n* @example\n* 0\n* @example\n* 21\n*/\n/**\n* @typedef {Number} NumberCIEDE\n* A CIEDE2000 difference between 2 colors (0 - 100)\n* @example\n* 0\n* @example\n* 100\n*/\n", "/**\n * Copyright (C) 2010-2021 <PERSON> Breach\n *\n * This program is free software: you can redistribute it and/or modify\n * it under the terms of the GNU Lesser General Public License as published by\n * the Free Software Foundation, either version 3 of the License, or\n * (at your option) any later version.\n *\n * This program is distributed in the hope that it will be useful,\n * but WITHOUT ANY WARRANTY; without even the implied warranty of\n * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n * GNU Lesser General Public License for more details.\n *\n * You should have received a copy of the GNU Lesser General Public License\n * along with this program.  If not, see <http://www.gnu.org/licenses/>.\n */\n/**\n * TagCanvas 2.11\n * For more information, please contact <<EMAIL>>\n */\nexport const tagCanvasString = `(function(){\"use strict\"; var r,C,p=Math.abs,o=Math.sin,l=Math.cos,g=Math.max,h=Math.min,af=Math.ceil,E=Math.sqrt,w=Math.pow,I={},D={},R={0:\"0,\",1:\"17,\",2:\"34,\",3:\"51,\",4:\"68,\",5:\"85,\",6:\"102,\",7:\"119,\",8:\"136,\",9:\"153,\",a:\"170,\",A:\"170,\",b:\"187,\",B:\"187,\",c:\"204,\",C:\"204,\",d:\"221,\",D:\"221,\",e:\"238,\",E:\"238,\",f:\"255,\",F:\"255,\"},f,d,b,T,z,F,M,c=document,v,e,P,j={};for(r=0;r<256;++r)C=r.toString(16),r<16&&(C='0'+C),D[C]=D[C.toUpperCase()]=r.toString()+',';function n(a){return typeof a!='undefined'}function B(a){return typeof a=='object'&&a!=null}function G(a,c,b){return isNaN(a)?b:h(b,g(c,a))}function x(){return!1}function q(){return(new Date).valueOf()}function ak(c,d){var b=[],e=c.length,a;for(a=0;a<e;++a)b.push(c[a]);return b.sort(d),b}function ai(a){for(var b=a.length-1,d,c;b;)c=~~(Math.random()*b),d=a[b],a[b]=a[c],a[c]=d,--b}function ag(){var a=window.AudioContext||window.webkitAudioContext;if(e=new a,!e){e='off';return}return e}function $(u,a,t,s,r,q,p){var j=s,h=r,i=t*.01,n=80*i,m=100*i,o=40*i,l=30*i,c=l/2,b=j+n,f=b-o,k=h+m,d=k-l,g=h+l,e=h+m/2;if(a.setTransform(1,0,0,1,0,0),a.setLineDash([]),a.globalAlpha=1,a.strokeStyle=p,a.lineWidth=q,a.lineJoin='round',a.beginPath(),a.moveTo(f,g),a.lineTo(f,d),a.moveTo(b,k),a.lineTo(f,d),a.lineTo(j,d),a.lineTo(j,g),a.lineTo(f,g),a.lineTo(b,h),u){a.lineTo(b,g),a.moveTo(b,d),a.lineTo(b,k),a.moveTo(b-c,e-c),a.lineTo(b+c,e+c),a.moveTo(b+c,e-c),a.lineTo(b-c,e+c),a.stroke();return}a.closePath(),a.stroke()}function s(a,b,c){this.x=a,this.y=b,this.z=c}z=s.prototype,z.length=function(){return E(this.x*this.x+this.y*this.y+this.z*this.z)},z.dot=function(a){return this.x*a.x+this.y*a.y+this.z*a.z},z.cross=function(a){var b=this.y*a.z-this.z*a.y,c=this.z*a.x-this.x*a.z,d=this.x*a.y-this.y*a.x;return new s(b,c,d)},z.angle=function(b){var c=this.dot(b),a;return c==0?Math.PI/2:(a=c/(this.length()*b.length()),a>=1)?0:a<=-1?Math.PI:Math.acos(a)},z.unit=function(){var a=this.length();return new s(this.x/a,this.y/a,this.z/a)};function ay(b,a){a=a*Math.PI/180,b=b*Math.PI/180;var c=o(b)*l(a),d=-o(a),e=-l(b)*l(a);return new s(c,d,e)}function m(a){this[1]={1:a[0],2:a[1],3:a[2]},this[2]={1:a[3],2:a[4],3:a[5]},this[3]={1:a[6],2:a[7],3:a[8]}}T=m.prototype,m.Identity=function(){return new m([1,0,0,0,1,0,0,0,1])},m.Rotation=function(e,a){var c=o(e),d=l(e),b=1-d;return new m([d+w(a.x,2)*b,a.x*a.y*b-a.z*c,a.x*a.z*b+a.y*c,a.y*a.x*b+a.z*c,d+w(a.y,2)*b,a.y*a.z*b-a.x*c,a.z*a.x*b-a.y*c,a.z*a.y*b+a.x*c,d+w(a.z,2)*b])},T.mul=function(c){var d=[],a,b,e=c.xform?1:0;for(a=1;a<=3;++a)for(b=1;b<=3;++b)e?d.push(this[a][1]*c[1][b]+this[a][2]*c[2][b]+this[a][3]*c[3][b]):d.push(this[a][b]*c);return new m(d)},T.xform=function(b){var a={},c=b.x,d=b.y,e=b.z;return a.x=c*this[1][1]+d*this[2][1]+e*this[3][1],a.y=c*this[1][2]+d*this[2][2]+e*this[3][2],a.z=c*this[1][3]+d*this[2][3]+e*this[3][3],a};function aB(g,j,k,m,f){var a,b,c,d,e=[],h=2/g,i;i=Math.PI*(3-E(5)+(parseFloat(f)?parseFloat(f):0));for(a=0;a<g;++a)b=a*h-1+h/2,c=E(1-b*b),d=a*i,e.push([l(d)*c*j,b*k,o(d)*c*m]);return e}function U(n,p,m,k,h,g){var b,f=[],i=2/n,j,a,d,c,e;j=Math.PI*(3-E(5)+(parseFloat(g)?parseFloat(g):0));for(a=0;a<n;++a)d=a*i-1+i/2,b=a*j,c=l(b),e=o(b),f.push(p?[d*m,c*k,e*h]:[c*m,d*k,e*h]);return f}function aa(k,e,f,h,i,j){var b,g=[],m=Math.PI*2/e,a,c,d;for(a=0;a<e;++a)b=a*m,c=l(b),d=o(b),g.push(k?[j*f,c*h,d*i]:[c*f,j*h,d*i]);return g}function ax(a,b,c,d,e){return U(a,0,b,c,d,e)}function aH(a,b,c,d,e){return U(a,1,b,c,d,e)}function aG(b,c,d,e,a){return a=isNaN(a)?0:a*1,aa(0,b,c,d,e,a)}function aF(b,c,d,e,a){return a=isNaN(a)?0:a*1,aa(1,b,c,d,e,a)}function av(b){var a=new Image;a.onload=function(){var c=a.width/2,d=a.height/2;b.centreFunc=function(b,g,h,e,f){b.setTransform(1,0,0,1,0,0),b.globalAlpha=1,b.drawImage(a,e-c,f-d)}},a.src=b.centreImage}function aE(a,c){var b=a,d,e,f=(c*1).toPrecision(3)+')';return a[0]==='#'?(I[a]||(a.length===4?I[a]='rgba('+R[a[1]]+R[a[2]]+R[a[3]]:I[a]='rgba('+D[a.substr(1,2)]+D[a.substr(3,2)]+D[a.substr(5,2)]),b=I[a]+f):a.substr(0,4)==='rgb('||a.substr(0,4)==='hsl('?b=a.replace('(','a(').replace(')',','+f):(a.substr(0,5)==='rgba('||a.substr(0,5)==='hsla(')&&(d=a.lastIndexOf(',')+1,e=a.indexOf(')'),c*=parseFloat(a.substring(d,e)),b=a.substr(0,d)+c.toPrecision(3)+')'),b}function k(b,d){if(window.G_vmlCanvasManager)return null;var a=c.createElement('canvas');return a.width=b,a.height=d,a}function aD(){var b=k(3,3),a,c;return!!b&&(a=b.getContext('2d'),a.strokeStyle='#000',a.shadowColor='#fff',a.shadowBlur=3,a.globalAlpha=0,a.strokeRect(2,2,2,2),a.globalAlpha=1,c=a.getImageData(2,2,1,1),b=null,c.data[0]>0)}function aC(a,c,f,d){var e=a.createLinearGradient(0,0,c,0),b;for(b in d)e.addColorStop(1-b,d[b]);a.fillStyle=e,a.fillRect(0,f,c,1)}function L(a,m,j){var l=1024,d=1,e=a.weightGradient,i,f,b,c;if(a.gCanvas)f=a.gCanvas.getContext('2d'),d=a.gCanvas.height;else{if(B(e[0])?d=e.length:e=[e],a.gCanvas=i=k(l,d),!i)return null;f=i.getContext('2d');for(b=0;b<d;++b)aC(f,l,b,e[b])}return j=g(h(j||0,d-1),0),c=f.getImageData(~~((l-1)*m),j,1,1).data,'rgba('+c[0]+','+c[1]+','+c[2]+','+c[3]/255+')'}function Y(b,i,q,k,o,n,h,d,a,g,f,l){var m=o+(d||0)+(a.length&&a[0]<0?p(a[0]):0),j=n+(d||0)+(a.length&&a[1]<0?p(a[1]):0),c,e;b.font=i,b.textBaseline='top',b.fillStyle=q,h&&(b.shadowColor=h),d&&(b.shadowBlur=d),a.length&&(b.shadowOffsetX=a[0],b.shadowOffsetY=a[1]);for(c=0;c<k.length;++c)e=0,f&&('right'==l?e=g-f[c]:'centre'==l&&(e=(g-f[c])/2)),b.fillText(k[c],m+e,j),j+=parseInt(i)}function y(d,a,b,f,e,c,g){c?(d.beginPath(),d.moveTo(a,b+e-c),d.arcTo(a,b,a+c,b,c),d.arcTo(a+f,b,a+f,b+c,c),d.arcTo(a+f,b+e,a+f-c,b+e,c),d.arcTo(a,b+e,a,b+e-c,c),d.closePath(),d[g?'stroke':'fill']()):d[g?'strokeRect':'fillRect'](a,b,f,e)}function O(a,b,c,d,e,f,g,h,i){this.strings=a,this.font=b,this.width=c,this.height=d,this.maxWidth=e,this.stringWidths=f,this.align=g,this.valign=h,this.scale=i}M=O.prototype,M.SetImage=function(a,b,c,d,e,f,g,h){this.image=a,this.iwidth=b*this.scale,this.iheight=c*this.scale,this.ipos=d,this.ipad=e*this.scale,this.iscale=h,this.ialign=f,this.ivalign=g},M.Align=function(c,d,a){var b=0;return a=='right'||a=='bottom'?b=d-c:a!='left'&&a!='top'&&(b=(d-c)/2),b},M.Create=function(G,D,F,b,A,m,q,j,E){var o,e,f,a,l,s,i,u,v,r,w,n,c,d,x,B=p(q[0]),C=p(q[1]),t,z;return j=g(j,B+m,C+m),l=2*(j+b),i=2*(j+b),e=this.width+l,f=this.height+i,v=r=j+b,this.image&&(w=n=j+b,c=this.iwidth,d=this.iheight,this.ipos=='top'||this.ipos=='bottom'?(c<this.width?w+=this.Align(c,this.width,this.ialign):v+=this.Align(this.width,c,this.align),this.ipos=='top'?r+=d+this.ipad:n+=this.height+this.ipad,e=g(e,c+l),f+=d+this.ipad):(d<this.height?n+=this.Align(d,this.height,this.ivalign):r+=this.Align(this.height,d,this.valign),this.ipos=='right'?w+=this.width+this.ipad:v+=c+this.ipad,e+=c+this.ipad,f=g(f,d+i))),o=k(e,f),!o?null:(l=i=b/2,s=e-b,u=f-b,x=h(E,s/2,u/2),a=o.getContext('2d'),D&&(a.fillStyle=D,y(a,l,i,s,u,x)),b&&(a.strokeStyle=F,a.lineWidth=b,y(a,l,i,s,u,x,!0)),(m||B||C)&&(t=k(e,f),t&&(z=a,a=t.getContext('2d'))),Y(a,this.font,G,this.strings,v,r,0,0,[],this.maxWidth,this.stringWidths,this.align),this.image&&a.drawImage(this.image,w,n,c,d),z&&(a=z,A&&(a.shadowColor=A),m&&(a.shadowBlur=m),a.shadowOffsetX=q[0],a.shadowOffsetY=q[1],a.drawImage(t,0,0)),o)};function H(a,c,d){var b=k(c,d),e;return b?(e=b.getContext('2d'),e.drawImage(a,(c-a.width)/2,(d-a.height)/2),b):null}function S(e,b,c){var a=k(b,c),d;return a?(d=a.getContext('2d'),d.drawImage(e,0,0,b,c),a):null}function W(n,u,t,e,s,c,v,d,r,w){var g=u+(2*d+c)*e,f=t+(2*d+c)*e,l=k(g,f),b,i,q,m,j,o,a,p;return l?(c*=e,r*=e,i=q=c/2,m=g-c,j=f-c,d=d*e+i,b=l.getContext('2d'),p=h(r,m/2,j/2),s&&(b.fillStyle=s,y(b,i,q,m,j,p)),c&&(b.strokeStyle=v,b.lineWidth=c,y(b,i,q,m,j,p,!0)),w?(o=k(g,f),a=o.getContext('2d'),a.drawImage(n,d,d,u,t),a.globalCompositeOperation='source-in',a.fillStyle=v,a.fillRect(0,0,g,f),a.globalCompositeOperation='destination-over',a.drawImage(l,0,0),a.globalCompositeOperation='source-over',b.drawImage(o,0,0)):b.drawImage(n,d,d,n.width,n.height),{image:l,width:g/e,height:f/e}):null}function at(l,f,c,d,j){var e,a,b=parseFloat(f),i=g(c,d);return e=k(c,d),!e?null:(f.indexOf('%')>0?b=i*b/100:b=b*j,a=e.getContext('2d'),a.globalCompositeOperation='source-over',a.fillStyle='#fff',b>=i/2?(b=h(c,d)/2,a.beginPath(),a.moveTo(c/2,d/2),a.arc(c/2,d/2,b,0,2*Math.PI,!1),a.fill(),a.closePath()):(b=h(c/2,d/2,b),y(a,0,0,c,d,b,!0),a.fill()),a.globalCompositeOperation='source-in',a.drawImage(l,0,0,c,d),e)}function ao(q,m,i,b,h,a,c){var g=p(c[0]),f=p(c[1]),j=m+(g>a?g+a:a*2)*b,l=i+(f>a?f+a:a*2)*b,n=b*((a||0)+(c[0]<0?g:0)),o=b*((a||0)+(c[1]<0?f:0)),e,d;return e=k(j,l),!e?null:(d=e.getContext('2d'),h&&(d.shadowColor=h),a&&(d.shadowBlur=a*b),c&&(d.shadowOffsetX=c[0]*b,d.shadowOffsetY=c[1]*b),d.drawImage(q,n,o,m,i),{image:e,width:j/b,height:l/b})}function ae(m,o,l){var c=parseInt(m.toString().length*l),h=parseInt(l*2*m.length),j=k(c,h),g,i,e,f,b,d,n,a;if(!j)return null;g=j.getContext('2d'),g.fillStyle='#000',g.fillRect(0,0,c,h),Y(g,l+'px '+o,'#fff',m,0,0,0,0,[],'centre'),i=g.getImageData(0,0,c,h),e=i.width,f=i.height,a={min:{x:e,y:f},max:{x:-1,y:-1}};for(d=0;d<f;++d)for(b=0;b<e;++b)n=(d*e+b)*4,i.data[n+1]>0&&(b<a.min.x&&(a.min.x=b),b>a.max.x&&(a.max.x=b),d<a.min.y&&(a.min.y=d),d>a.max.y&&(a.max.y=d));return e!=c&&(a.min.x*=c/e,a.max.x*=c/e),f!=h&&(a.min.y*=c/f,a.max.y*=c/f),j=null,a}function Q(a){return\"'\"+a.replace(/(\\'|\\\")/g,'').replace(/\\s*,\\s*/g,\"', '\")+\"'\"}function t(b,d,a){a=a||c,a.addEventListener?a.addEventListener(b,d,!1):a.attachEvent('on'+b,d)}function am(b,d,a){a=a||c,a.removeEventListener?a.removeEventListener(b,d):a.detachEvent('on'+b,d)}function A(g,e,j,a,b){var l=b.imageScale,h,c,k,m,f,d;if(!e.complete)return t('load',function(){A(g,e,j,a,b)},e);if(!g.complete)return t('load',function(){A(g,e,j,a,b)},g);if(j&&!j.complete)return t('load',function(){A(g,e,j,a,b)},j);e.width=e.width,e.height=e.height,l&&(g.width=e.width*l,g.height=e.height*l),a.iw=g.width,a.ih=g.height,b.txtOpt&&(c=g,h=b.zoomMax*b.txtScale,f=a.iw*h,d=a.ih*h,f<e.naturalWidth||d<e.naturalHeight?(c=S(g,f,d),c&&(a.fimage=c)):(f=a.iw,d=a.ih,h=1),parseFloat(b.imageRadius)&&(a.image=a.fimage=g=at(a.image,b.imageRadius,f,d,h)),a.HasText()||(b.shadow&&(c=ao(a.image,f,d,h,b.shadow,b.shadowBlur,b.shadowOffset),c&&(a.fimage=c.image,a.w=c.width,a.h=c.height)),(b.bgColour||b.bgOutlineThickness)&&(k=b.bgColour=='tag'?i(a.a,'background-color'):b.bgColour,m=b.bgOutline=='tag'?i(a.a,'color'):b.bgOutline||b.textColour,f=a.fimage.width,d=a.fimage.height,b.outlineMethod=='colour'&&(c=W(a.fimage,f,d,h,k,b.bgOutlineThickness,a.outline.colour,b.padding,b.bgRadius,1),c&&(a.oimage=c.image)),c=W(a.fimage,f,d,h,k,b.bgOutlineThickness,m,b.padding,b.bgRadius),c&&(a.fimage=c.image,a.w=c.width,a.h=c.height)),b.outlineMethod=='size'&&(b.outlineIncrease>0?(a.iw+=2*b.outlineIncrease,a.ih+=2*b.outlineIncrease,f=h*a.iw,d=h*a.ih,c=S(a.fimage,f,d),a.oimage=c,a.fimage=H(a.fimage,a.oimage.width,a.oimage.height)):(f=h*(a.iw+2*b.outlineIncrease),d=h*(a.ih+2*b.outlineIncrease),c=S(a.fimage,f,d),a.oimage=H(c,a.fimage.width,a.fimage.height))))),a.alt=j,a.Init()}function i(a,d){var b=c.defaultView,e=d.replace(/\\-([a-z])/g,function(a){return a.charAt(1).toUpperCase()});return b&&b.getComputedStyle&&b.getComputedStyle(a,null).getPropertyValue(d)||a.currentStyle&&a.currentStyle[e]}function aj(c,d,e){var b=1,a;return d?b=1*(c.getAttribute(d)||e):(a=i(c,'font-size'))&&(b=a.indexOf('px')>-1&&a.replace('px','')*1||a.indexOf('pt')>-1&&a.replace('pt','')*1.25||a*3.3),b}function u(a){return a.target&&n(a.target.id)?a.target.id:a.srcElement.parentNode.id}function K(a,c){var b,d,e=parseInt(i(c,'width'))/c.width,f=parseInt(i(c,'height'))/c.height;return n(a.offsetX)?b={x:a.offsetX,y:a.offsetY}:(d=X(c.id),n(a.changedTouches)&&(a=a.changedTouches[0]),a.pageX&&(b={x:a.pageX-d.x,y:a.pageY-d.y})),b&&e&&f&&(b.x/=e,b.y/=f),b}function an(c){var d=c.target||c.fromElement.parentNode,b=a.tc[d.id];b&&(b.mx=b.my=-1,b.UnFreeze(),b.EndDrag())}function ad(e){var g,c=a,b,d,f=u(e);for(g in c.tc)b=c.tc[g],b.tttimer&&(clearTimeout(b.tttimer),b.tttimer=null);f&&c.tc[f]&&(b=c.tc[f],(d=K(e,b.canvas))&&(b.mx=d.x,b.my=d.y,b.Drag(e,d)),b.drawn=0)}function ap(b){var e=a,f=c.addEventListener?0:1,d=u(b);d&&b.button==f&&e.tc[d]&&e.tc[d].BeginDrag(b)}function aq(b){var f=a,g=c.addEventListener?0:1,e=u(b),d;e&&b.button==g&&f.tc[e]&&(d=f.tc[e],ad(b),!d.EndDrag()&&!d.touchState&&d.Clicked(b))}function ar(c){var e=u(c),b=e&&a.tc[e],d;b&&c.changedTouches&&(c.touches.length==1&&b.touchState==0?(b.touchState=1,b.BeginDrag(c),(d=K(c,b.canvas))&&(b.mx=d.x,b.my=d.y,b.drawn=0)):c.targetTouches.length==2&&b.pinchZoom?(b.touchState=3,b.EndDrag(),b.BeginPinch(c)):(b.EndDrag(),b.EndPinch(),b.touchState=0))}function ac(c){var d=u(c),b=d&&a.tc[d];if(b&&c.changedTouches){switch(b.touchState){case 1:b.Draw(),b.Clicked();break;break;case 2:b.EndDrag();break;case 3:b.EndPinch()}b.touchState=0}}function au(c){var f,e=a,b,d,g=u(c);for(f in e.tc)b=e.tc[f],b.tttimer&&(clearTimeout(b.tttimer),b.tttimer=null);if(b=g&&e.tc[g],b&&c.changedTouches&&b.touchState){switch(b.touchState){case 1:case 2:(d=K(c,b.canvas))&&(b.mx=d.x,b.my=d.y,b.Drag(c,d)&&(b.touchState=2));break;case 3:b.Pinch(c)}b.drawn=0}}function ab(b){var d=a,c=u(b);c&&d.tc[c]&&(b.cancelBubble=!0,b.returnValue=!1,b.preventDefault&&b.preventDefault(),d.tc[c].Wheel((b.wheelDelta||b.detail)>0))}function aw(d){var c,b=a;clearTimeout(b.scrollTimer);for(c in b.tc)b.tc[c].Pause();b.scrollTimer=setTimeout(function(){var b,c=a;for(b in c.tc)c.tc[b].Resume()},b.scrollPause)}function al(){Z(q())}function Z(b){var c=a.tc,d;a.NextFrame(a.interval),b=b||q();for(d in c)c[d].Draw(b)}function az(){requestAnimationFrame(Z)}function aA(a){setTimeout(al,a)}function X(f){var g=c.getElementById(f),b=g.getBoundingClientRect(),a=c.documentElement,d=c.body,e=window,h=e.pageXOffset||a.scrollLeft,i=e.pageYOffset||a.scrollTop,j=a.clientLeft||d.clientLeft,k=a.clientTop||d.clientTop;return{x:b.left+h-j,y:b.top+i-k}}function aI(a,b,d,e){var c=a.radius*a.z1/(a.z1+a.z2+b.z);return{x:b.x*c*d,y:b.y*c*e,z:b.z,w:(a.z1-b.z)/a.z2}}function V(a){this.e=a,this.br=0,this.line=[],this.text=[],this.original=a.innerText||a.textContent}F=V.prototype,F.Empty=function(){for(var a=0;a<this.text.length;++a)if(this.text[a].length)return!1;return!0},F.Lines=function(c){var e=c?1:0,b,d,a;c=c||this.e,b=c.childNodes,d=b.length;for(a=0;a<d;++a)b[a].nodeName=='BR'?(this.text.push(this.line.join(' ')),this.br=1):b[a].nodeType==3?this.br?(this.line=[b[a].nodeValue],this.br=0):this.line.push(b[a].nodeValue):this.Lines(b[a]);return e||this.br||this.text.push(this.line.join(' ')),this.text},F.SplitWidth=function(h,e,f,g){var c,b,a,d=[];e.font=g+'px '+f;for(c=0;c<this.text.length;++c){a=this.text[c].split(/\\s+/),this.line=[a[0]];for(b=1;b<a.length;++b)e.measureText(this.line.join(' ')+' '+a[b]).width>h?(d.push(this.line.join(' ')),this.line=[a[b]]):this.line.push(a[b]);d.push(this.line.join(' '))}return this.text=d};function _(a,b){this.ts=null,this.tc=a,this.tag=b,this.x=this.y=this.w=this.h=this.sc=1,this.z=0,this.pulse=1,this.pulsate=a.pulsateTo<1,this.colour=a.outlineColour,this.adash=~~a.outlineDash,this.agap=~~a.outlineDashSpace||this.adash,this.aspeed=a.outlineDashSpeed*1,this.colour=='tag'?this.colour=i(b.a,'color'):this.colour=='tagbg'&&(this.colour=i(b.a,'background-color')),this.Draw=this.pulsate?this.DrawPulsate:this.DrawSimple,this.radius=a.outlineRadius|0,this.SetMethod(a.outlineMethod,a.altImage)}f=_.prototype,f.SetMethod=function(a,d){var b={block:['PreDraw','DrawBlock'],colour:['PreDraw','DrawColour'],outline:['PostDraw','DrawOutline'],classic:['LastDraw','DrawOutline'],size:['PreDraw','DrawSize'],none:['LastDraw']},c=b[a]||b.outline;a=='none'?this.Draw=function(){return 1}:this.drawFunc=this[c[1]],this[c[0]]=this.Draw,d&&(this.RealPreDraw=this.PreDraw,this.PreDraw=this.DrawAlt)},f.Update=function(d,e,i,j,a,f,g,h){var b=this.tc.outlineOffset,c=2*b;this.x=a*d+g-b,this.y=a*e+h-b,this.w=a*i+c,this.h=a*j+c,this.sc=a,this.z=f},f.Ants=function(k){if(!this.adash)return;var b=this.adash,c=this.agap,a=this.aspeed,j=b+c,h=0,g=b,f=c,i=0,d=0,e;a&&(d=p(a)*(q()-this.ts)/50,a<0&&(d=864e4-d),a=~~d%j),a?(b>=a?(h=b-a,g=a):(f=j-a,i=c-f),e=[h,f,g,i]):e=[b,c],k.setLineDash(e)},f.DrawOutline=function(a,d,e,b,c,f){var g=h(this.radius,c/2,b/2);a.strokeStyle=f,this.Ants(a),y(a,d,e,b,c,g,!0)},f.DrawSize=function(i,n,m,l,k,j,a,h,g){var f=a.w,e=a.h,c,b,d;return this.pulsate?(a.image?d=(a.image.height+this.tc.outlineIncrease)/a.image.height:d=a.oscale,b=a.fimage||a.image,c=1+(d-1)*(1-this.pulse),a.h*=c,a.w*=c):b=a.oimage,a.alpha=1,a.Draw(i,h,g,b),a.h=e,a.w=f,1},f.DrawColour=function(d,h,i,e,f,g,a,b,c){return a.oimage?(this.pulse<1?(a.alpha=1-w(this.pulse,2),a.Draw(d,b,c,a.fimage),a.alpha=this.pulse):a.alpha=1,a.Draw(d,b,c,a.oimage),1):this[a.image?'DrawColourImage':'DrawColourText'](d,h,i,e,f,g,a,b,c)},f.DrawColourText=function(f,h,i,j,g,e,a,b,c){var d=a.colour;return a.colour=e,a.alpha=1,a.Draw(f,b,c),a.colour=d,1},f.DrawColourImage=function(a,q,p,o,n,m,i,r,l){var f=a.canvas,e=~~g(q,0),d=~~g(p,0),c=h(f.width-e,o)+.5|0,b=h(f.height-d,n)+.5|0,j;return v?(v.width=c,v.height=b):v=k(c,b),!v?this.SetMethod('outline'):(j=v.getContext('2d'),j.drawImage(f,e,d,c,b,0,0,c,b),a.clearRect(e,d,c,b),this.pulsate?i.alpha=1-w(this.pulse,2):i.alpha=1,i.Draw(a,r,l),a.setTransform(1,0,0,1,0,0),a.save(),a.beginPath(),a.rect(e,d,c,b),a.clip(),a.globalCompositeOperation='source-in',a.fillStyle=m,a.fillRect(e,d,c,b),a.restore(),a.globalAlpha=1,a.globalCompositeOperation='destination-over',a.drawImage(v,0,0,c,b,e,d,c,b),a.globalCompositeOperation='source-over',1)},f.DrawAlt=function(b,a,c,d,f,g){var e=this.RealPreDraw(b,a,c,d,f,g);return a.alt&&(a.DrawImage(b,c,d,a.alt),e=1),e},f.DrawBlock=function(a,d,e,b,c,f){var g=h(this.radius,c/2,b/2);a.fillStyle=f,y(a,d,e,b,c,g)},f.DrawSimple=function(a,b,c,d,e,f){var g=this.tc;return a.setTransform(1,0,0,1,0,0),a.strokeStyle=this.colour,a.lineWidth=g.outlineThickness,a.shadowBlur=a.shadowOffsetX=a.shadowOffsetY=0,a.globalAlpha=f?e:1,this.drawFunc(a,this.x,this.y,this.w,this.h,this.colour,b,c,d)},f.DrawPulsate=function(h,d,e,f){var g=q()-this.ts,c=this.tc,b=c.pulsateTo+(1-c.pulsateTo)*(.5+l(2*Math.PI*g/(1e3*c.pulsateTime))/2);return this.pulse=b=a.Smooth(1,b),this.DrawSimple(h,d,e,f,b,1)},f.Active=function(d,a,b){var c=a>=this.x&&b>=this.y&&a<=this.x+this.w&&b<=this.y+this.h;return c?this.ts=this.ts||q():this.ts=null,c},f.PreDraw=f.PostDraw=f.LastDraw=x;function J(a,h,c,b,e,f,g,d,i,j,k,l,m,n){this.tc=a,this.image=null,this.text=h,this.text_original=n,this.line_widths=[],this.title=c.title||null,this.a=c,this.position=new s(b[0],b[1],b[2]),this.x=this.y=this.z=0,this.w=e,this.h=f,this.colour=g||a.textColour,this.bgColour=d||a.bgColour,this.bgRadius=i|0,this.bgOutline=j||this.colour,this.bgOutlineThickness=k|0,this.textFont=l||a.textFont,this.padding=m|0,this.sc=this.alpha=1,this.weighted=!a.weight,this.outline=new _(a,this),this.audio=null}d=J.prototype,d.Init=function(b){var a=this.tc;this.textHeight=a.textHeight,this.HasText()?this.Measure(a.ctxt,a):(this.w=this.iw,this.h=this.ih),this.SetShadowColour=a.shadowAlpha?this.SetShadowColourAlpha:this.SetShadowColourFixed,this.SetDraw(a)},d.Draw=x,d.HasText=function(){return this.text&&this.text[0].length>0},d.EqualTo=function(a){var b=a.getElementsByTagName('img');return this.a.href!=a.href?0:b.length?this.image.src==b[0].src:(a.innerText||a.textContent)==this.text_original},d.SetImage=function(a){this.image=this.fimage=a},d.SetAudio=function(a){this.audio=a,this.audio.load()},d.SetDraw=function(a){this.Draw=this.fimage?a.ie>7?this.DrawImageIE:this.DrawImage:this.DrawText,a.noSelect&&(this.CheckActive=x)},d.MeasureText=function(d){var a,e=this.text.length,b=0,c;for(a=0;a<e;++a)this.line_widths[a]=c=d.measureText(this.text[a]).width,b=g(b,c);return b},d.Measure=function(e,a){var f=ae(this.text,this.textFont,this.textHeight),b,k,h,i,g,l,j,c,d;j=f?f.max.y+f.min.y:this.textHeight,e.font=this.font=this.textHeight+'px '+this.textFont,l=this.MeasureText(e),a.txtOpt&&(b=a.txtScale,k=b*this.textHeight,h=k+'px '+this.textFont,i=[b*a.shadowOffset[0],b*a.shadowOffset[1]],e.font=h,g=this.MeasureText(e),d=new O(this.text,h,g+b,b*j+b,g,this.line_widths,a.textAlign,a.textVAlign,b),this.image&&d.SetImage(this.image,this.iw,this.ih,a.imagePosition,a.imagePadding,a.imageAlign,a.imageVAlign,a.imageScale),c=d.Create(this.colour,this.bgColour,this.bgOutline,b*this.bgOutlineThickness,a.shadow,b*a.shadowBlur,i,b*this.padding,b*this.bgRadius),a.outlineMethod=='colour'?this.oimage=d.Create(this.outline.colour,this.bgColour,this.outline.colour,b*this.bgOutlineThickness,a.shadow,b*a.shadowBlur,i,b*this.padding,b*this.bgRadius):a.outlineMethod=='size'&&(f=ae(this.text,this.textFont,this.textHeight+a.outlineIncrease),k=f.max.y+f.min.y,h=b*(this.textHeight+a.outlineIncrease)+'px '+this.textFont,e.font=h,g=this.MeasureText(e),d=new O(this.text,h,g+b,b*k+b,g,this.line_widths,a.textAlign,a.textVAlign,b),this.image&&d.SetImage(this.image,this.iw+a.outlineIncrease,this.ih+a.outlineIncrease,a.imagePosition,a.imagePadding,a.imageAlign,a.imageVAlign,a.imageScale),this.oimage=d.Create(this.colour,this.bgColour,this.bgOutline,b*this.bgOutlineThickness,a.shadow,b*a.shadowBlur,i,b*this.padding,b*this.bgRadius),this.oscale=this.oimage.width/c.width,a.outlineIncrease>0?c=H(c,this.oimage.width,this.oimage.height):this.oimage=H(this.oimage,c.width,c.height)),c&&(this.fimage=c,l=this.fimage.width/b,j=this.fimage.height/b),this.SetDraw(a),a.txtOpt=!!this.fimage),this.h=j,this.w=l},d.SetFont=function(a,b,c,d){this.textFont=a,this.colour=b,this.bgColour=c,this.bgOutline=d,this.Measure(this.tc.ctxt,this.tc)},d.SetWeight=function(c){var b=this.tc,e=b.weightMode.split(/[, ]/),d,a,f=c.length;if(!this.HasText())return;this.weighted=!0;for(a=0;a<f;++a)d=e[a]||'size','both'==d?(this.Weight(c[a],b.ctxt,b,'size',b.min_weight[a],b.max_weight[a],a),this.Weight(c[a],b.ctxt,b,'colour',b.min_weight[a],b.max_weight[a],a)):this.Weight(c[a],b.ctxt,b,d,b.min_weight[a],b.max_weight[a],a);this.Measure(b.ctxt,b)},d.Weight=function(b,i,a,d,f,h,e){b=isNaN(b)?1:b;var c=(b-f)/(h-f);'colour'==d?this.colour=L(a,c,e):'bgcolour'==d?this.bgColour=L(a,c,e):'bgoutline'==d?this.bgOutline=L(a,c,e):'outline'==d?this.outline.colour=L(a,c,e):'size'==d&&(a.weightSizeMin>0&&a.weightSizeMax>a.weightSizeMin?this.textHeight=a.weightSize*(a.weightSizeMin+(a.weightSizeMax-a.weightSizeMin)*c):this.textHeight=g(1,b*a.weightSize))},d.SetShadowColourFixed=function(a,b,c){a.shadowColor=b},d.SetShadowColourAlpha=function(a,b,c){a.shadowColor=aE(b,c)},d.DrawText=function(a,h,i){var e=this.tc,g=this.x,f=this.y,c=this.sc,b,d;a.globalAlpha=this.alpha,a.fillStyle=this.colour,e.shadow&&this.SetShadowColour(a,e.shadow,this.alpha),a.font=this.font,g+=h/c,f+=i/c-this.h/2;for(b=0;b<this.text.length;++b)d=g,'right'==e.textAlign?d+=this.w/2-this.line_widths[b]:'centre'==e.textAlign?d-=this.line_widths[b]/2:d-=this.w/2,a.setTransform(c,0,0,c,c*d,c*f),a.fillText(this.text[b],0,0),f+=this.textHeight},d.DrawImage=function(b,i,k,l){var e=this.x,f=this.y,a=this.sc,j=l||this.fimage,c=this.w,d=this.h,g=this.alpha,h=this.shadow;b.globalAlpha=g,h&&this.SetShadowColour(b,h,g),e+=i/a-c/2,f+=k/a-d/2,b.setTransform(a,0,0,a,a*e,a*f),b.drawImage(j,0,0,c,d)},d.DrawImageIE=function(b,d,e){var c=this.fimage,a=this.sc,f=c.width=this.w*a,g=c.height=this.h*a,h=this.x*a+d-f/2,i=this.y*a+e-g/2;b.setTransform(1,0,0,1,0,0),b.globalAlpha=this.alpha,b.drawImage(c,h,i)},d.Calc=function(g,e){var a,b=this.tc,d=b.minBrightness,f=b.maxBrightness,c=b.max_radius;return a=g.xform(this.position),this.xformed=a,a=aI(b,a,b.stretchX,b.stretchY),this.x=a.x,this.y=a.y,this.z=a.z,this.sc=a.w,this.alpha=e*G(d+(f-d)*(c-this.z)/(2*c),0,1),this.xformed},d.UpdateActive=function(h,e,f){var a=this.outline,b=this.w,c=this.h,d=this.x-b/2,g=this.y-c/2;return a.Update(d,g,b,c,this.sc,this.z,e,f),a},d.CheckActive=function(a,d,e){var b=this.tc,c=this.UpdateActive(a,d,e);return c.Active(a,b.mx,b.my)?c:null},d.Clicked=function(f){var b=this.a,a=b.target,d=b.href,e;if(a!=''&&a!='_self'){if(self.frames[a])self.frames[a].document.location=d;else{try{if(top.frames[a]){top.frames[a].document.location=d;return}}catch(a){}window.open(d,a)}return}if(c.createEvent){if(e=c.createEvent('MouseEvents'),e.initMouseEvent('click',1,1,window,0,0,0,0,0,0,0,0,0,0,null),!b.dispatchEvent(e))return}else if(b.fireEvent)if(!b.fireEvent('onclick'))return;c.location=d},d.StopAudio=function(){this.audio&&this.playing&&this.audio.pause(),this.stopped=1,this.playing=0},d.PlayAudio=function(){if(e==='off'||this.tc.audioOff)return;if(!e&&!ag())return;var a=this.tc.audio,c=this.tc.gain,d='suspended',b;if(this.audio)if(this.track||(this.track=e.createMediaElementSource(this.audio),this.gain=e.createGain(),this.track.connect(this.gain),this.gain.connect(e.destination)),a=this.audio,c=this.gain,!a.paused)return 1;if(a){if(e.state==d&&e.resume(),e.state==d)return;return c.gain.value=h(2,g(0,this.tc.audioVolume*1)),a.currentTime=0,this.stopped=0,b=a.play(),b!==void 0&&b.then(a=>{this.stopped?this.audio.pause():this.playing=1}),1}};function a(f,o,k){var d,i,b=c.getElementById(f),l=['id','class','innerHTML'];if(!b)throw 0;if(n(window.G_vmlCanvasManager)&&(b=window.G_vmlCanvasManager.initElement(b),this.ie=parseFloat(navigator.appVersion.split('MSIE')[1])),b&&(!b.getContext||!b.getContext('2d').fillText)){i=c.createElement('DIV');for(d=0;d<l.length;++d)i[l[d]]=b[l[d]];throw b.parentNode.insertBefore(i,b),b.parentNode.removeChild(b),0}for(d in a.options)this[d]=k&&n(k[d])?k[d]:n(a[d])?a[d]:a.options[d];if(this.canvas=b,this.ctxt=b.getContext('2d'),this.z1=250/g(this.depth,.001),this.z2=this.z1/this.zoom,this.radius=h(b.height,b.width)*.0075,this.max_radius=100,this.max_weight=[],this.min_weight=[],this.textFont=this.textFont&&Q(this.textFont),this.textHeight*=1,this.imageRadius=this.imageRadius.toString(),this.pulsateTo=G(this.pulsateTo,0,1),this.minBrightness=G(this.minBrightness,0,1),this.maxBrightness=G(this.maxBrightness,this.minBrightness,1),this.ctxt.textBaseline='top',this.lx=(this.lock+'').indexOf('x')+1,this.ly=(this.lock+'').indexOf('y')+1,this.frozen=this.dx=this.dy=this.fixedAnim=this.touchState=0,this.fixedAlpha=1,this.source=o||f,this.repeatTags=h(64,~~this.repeatTags),this.minTags=h(200,~~this.minTags),~~this.scrollPause>0?a.scrollPause=~~this.scrollPause:this.scrollPause=0,this.minTags>0&&this.repeatTags<1&&(d=this.GetTags().length)&&(this.repeatTags=af(this.minTags/d)-1),this.transform=m.Identity(),this.startTime=this.time=q(),this.mx=this.my=-1,this.centreImage&&av(this),this.Animate=this.dragControl?this.AnimateDrag:this.AnimatePosition,this.animTiming=typeof a[this.animTiming]=='function'?a[this.animTiming]:a.Smooth,this.shadowBlur||this.shadowOffset[0]||this.shadowOffset[1]?(this.ctxt.shadowColor=this.shadow,this.shadow=this.ctxt.shadowColor,this.shadowAlpha=aD()):delete this.shadow,this.activeAudio===!1?e='off':this.activeAudio&&this.LoadAudio(),this.Load(),o&&this.hideTags&&function(b){a.loaded?b.HideTags():t('load',function(){b.HideTags()},window)}(this),this.yaw=this.initial?this.initial[0]*this.maxSpeed:0,this.pitch=this.initial?this.initial[1]*this.maxSpeed:0,this.tooltip?(this.ctitle=b.title,b.title='',this.tooltip=='native'?this.Tooltip=this.TooltipNative:(this.Tooltip=this.TooltipDiv,this.ttdiv||(this.ttdiv=c.createElement('div'),this.ttdiv.className=this.tooltipClass,this.ttdiv.style.position='absolute',this.ttdiv.style.zIndex=b.style.zIndex+1,t('mouseover',function(a){a.target.style.display='none'},this.ttdiv),c.body.appendChild(this.ttdiv)))):this.Tooltip=this.TooltipNone,!this.noMouse&&!j[f]){j[f]=[['mousemove',ad],['mouseout',an],['mouseup',aq],['touchstart',ar],['touchend',ac],['touchcancel',ac],['touchmove',au]],this.dragControl&&(j[f].push(['mousedown',ap]),j[f].push(['selectstart',x])),this.wheelZoom&&(j[f].push(['mousewheel',ab]),j[f].push(['DOMMouseScroll',ab])),this.scrollPause&&j[f].push(['scroll',aw,window]);for(d=0;d<j[f].length;++d)i=j[f][d],t(i[0],i[1],i[2]?i[2]:b)}a.started||(a.NextFrame=window.requestAnimationFrame?az:aA,a.interval=this.interval,a.NextFrame(this.interval),a.started=1)}b=a.prototype,b.SourceElements=function(){return c.querySelectorAll?c.querySelectorAll('#'+this.source):[c.getElementById(this.source)]},b.HideTags=function(){var b=this.SourceElements(),a;for(a=0;a<b.length;++a)b[a].style.display='none'},b.GetTags=function(){var e=this.SourceElements(),c,f=[],a,b,d;for(d=0;d<=this.repeatTags;++d)for(a=0;a<e.length;++a){c=e[a].getElementsByTagName('a');for(b=0;b<c.length;++b)f.push(c[b])}return f},b.Message=function(j){var g=[],a,f,b=j.split(''),d,e,h,i;for(a=0;a<b.length;++a)b[a]!=' '&&(f=a-b.length/2,d=c.createElement('A'),d.href='#',d.innerText=b[a],h=100*o(f/9),i=-100*l(f/9),e=new J(this,b[a],d,[h,0,i],2,18,'#000','#fff',0,0,0,'monospace',2,b[a]),e.Init(),g.push(e));return g},b.AddAudio=function(b,c){if(e==='off')return;var a=b.getElementsByTagName('audio');a.length&&(c.SetAudio(a[0]),this.hasAudio=1)},b.CreateTag=function(b){var e,c,a,f,d,g,h,j,k=[0,0,0],l;if('text'!=this.imageMode)if(e=b.getElementsByTagName('img'),e.length)if(c=new Image,c.src=e[0].src,!this.imageMode)return a=new J(this,\"\",b,k,0,0),a.SetImage(c),A(c,e[0],e[1],a,this),this.AddAudio(b,a),a;if('image'!=this.imageMode&&(d=new V(b),f=d.Lines(),d.Empty()?d=null:(g=this.textFont||Q(i(b,'font-family')),this.splitWidth&&(f=d.SplitWidth(this.splitWidth,this.ctxt,g,this.textHeight)),h=this.bgColour=='tag'?i(b,'background-color'):this.bgColour,j=this.bgOutline=='tag'?i(b,'color'):this.bgOutline)),d||c)return a=new J(this,f,b,k,2,this.textHeight+2,this.textColour||i(b,'color'),h,this.bgRadius,j,this.bgOutlineThickness,g,this.padding,d&&d.original),c?(a.SetImage(c),A(c,e[0],e[1],a,this)):a.Init(),this.AddAudio(b,a),a},b.UpdateTag=function(a,b){var c=this.textColour||i(b,'color'),d=this.textFont||Q(i(b,'font-family')),e=this.bgColour=='tag'?i(b,'background-color'):this.bgColour,f=this.bgOutline=='tag'?i(b,'color'):this.bgOutline;a.a=b,a.title=b.title,(a.colour!=c||a.textFont!=d||a.bgColour!=e||a.bgOutline!=f)&&a.SetFont(d,c,e,f)},b.Weight=function(d){var f=d.length,c,b,a,e=[],g,h=this.weightFrom?this.weightFrom.split(/[, ]/):[null],i=h.length;for(b=0;b<f;++b){e[b]=[];for(a=0;a<i;++a)c=aj(d[b].a,h[a],this.textHeight),(!this.max_weight[a]||c>this.max_weight[a])&&(this.max_weight[a]=c),(!this.min_weight[a]||c<this.min_weight[a])&&(this.min_weight[a]=c),e[b][a]=c}for(a=0;a<i;++a)this.max_weight[a]>this.min_weight[a]&&(g=1);if(g)for(b=0;b<f;++b)d[b].SetWeight(e[b])},b.Load=function(){var c=this.GetTags(),b=[],d,k,l,h,i,j,f,a,e=[],m={sphere:aB,vcylinder:ax,hcylinder:aH,vring:aG,hring:aF};if(c.length){e.length=c.length;for(a=0;a<c.length;++a)e[a]=a;this.shuffleTags&&ai(e),h=100*this.radiusX,i=100*this.radiusY,j=100*this.radiusZ,this.max_radius=g(h,g(i,j));for(a=0;a<c.length;++a)k=this.CreateTag(c[e[a]]),k&&b.push(k);this.weight&&this.Weight(b,!0),this.shapeArgs?this.shapeArgs[0]=b.length:(l=this.shape.toString().split(/[(),]/),d=l.shift(),typeof window[d]=='function'?this.shape=window[d]:this.shape=m[d]||m.sphere,this.shapeArgs=[b.length,h,i,j].concat(l)),f=this.shape.apply(this,this.shapeArgs),this.listLength=b.length;for(a=0;a<b.length;++a)b[a].position=new s(f[a][0],f[a][1],f[a][2])}this.noTagsMessage&&!b.length&&(a=this.imageMode&&this.imageMode!='both'?this.imageMode+' ':'',b=this.Message('No '+a+'tags')),this.taglist=b},b.Update=function(){var e=this.GetTags(),d=[],j=this.taglist,k,f=[],c=[],h,i,g,a,b;if(!this.shapeArgs)return this.Load();if(e.length){g=this.listLength=e.length,i=j.length;for(a=0;a<i;++a)d.push(j[a]),c.push(a);for(a=0;a<g;++a){for(b=0,k=0;b<i;++b)j[b].EqualTo(e[a])&&(this.UpdateTag(d[b],e[a]),k=c[b]=-1);k||f.push(a)}for(a=0,b=0;a<i;++a)c[b]==-1?c.splice(b,1):++b;if(c.length){for(ai(c);c.length&&f.length;)a=c.shift(),b=f.shift(),d[a]=this.CreateTag(e[b]);for(c.sort(function(a,b){return a-b});c.length;)d.splice(c.pop(),1)}for(b=d.length/(f.length+1),a=0;f.length;)d.splice(af(++a*b),0,this.CreateTag(e[f.shift()]));this.shapeArgs[0]=g=d.length,h=this.shape.apply(this,this.shapeArgs);for(a=0;a<g;++a)d[a].position=new s(h[a][0],h[a][1],h[a][2]);this.weight&&this.Weight(d)}this.taglist=d},b.SetShadow=function(a){a.shadowBlur=this.shadowBlur,a.shadowOffsetX=this.shadowOffset[0],a.shadowOffsetY=this.shadowOffset[1]},b.LoadAudio=function(){if(!e&&!ag())return;this.audio=c.createElement('audio'),this.audio.src=this.activeAudio,this.track=e.createMediaElementSource(this.audio),this.gain=e.createGain(),this.track.connect(this.gain),this.gain.connect(e.destination),this.hasAudio=1,P=function(a){e.resume(),c.removeEventListener('click',P)},c.addEventListener('click',P)},b.ShowAudioIcon=function(){var a=this.audioIconSize,c=this.canvas,d=this.ctxt,k=c.width-a-3,f=c.height-a-3,g=this.audioIconThickness,h='#000',i='#fff',j=this.audioIconDark,b=this.audioOff,l='suspended';if(!e)return;b||(b=e.state===l),this.audioIcon&&this.hasAudio&&($(b,d,a,k,f,g+1,j?i:h),$(b,d,a,k,f,g,j?h:i))},b.CheckAudioIcon=function(){var a=this.audioIconSize,b=this.canvas,c=this.audioIconThickness/2,d=b.width-a-3-c,e=b.height-a-3-c;if(this.audioIcon&&this.mx>=d&&this.my>=e)return!0},b.ToggleAudio=function(){var a=this.audioOff||e&&e.state==='suspended';a||this.currentAudio&&this.currentAudio.StopAudio(),this.audioOff=!a},b.Draw=function(s){if(this.paused)return;var l=this.canvas,i=l.width,j=l.height,q=0,p=(s-this.time)*a.interval/1e3,h=i/2+this.offsetX,g=j/2+this.offsetY,d=this.ctxt,b,f,c,o=-1,e=this.taglist,k=e.length,t=this.active&&this.active.tag,m='',u=this.frontSelect,r=this.centreFunc==x,n;if(this.time=s,this.frozen&&this.drawn)return this.Animate(i,j,p);n=this.AnimateFixed(),d.setTransform(1,0,0,1,0,0);for(c=0;c<k;++c)e[c].Calc(this.transform,this.fixedAlpha);if(e=ak(e,function(a,b){return b.z-a.z}),n&&this.fixedAnim.active)b=this.fixedAnim.tag.UpdateActive(d,h,g);else if(this.active=null,this.CheckAudioIcon())m='pointer';else{for(c=0;c<k;++c)f=this.mx>=0&&this.my>=0&&this.taglist[c].CheckActive(d,h,g),f&&f.sc>q&&(!u||f.z<=0)&&(b=f,o=c,b.tag=this.taglist[c],q=f.sc);this.active=b}this.txtOpt||this.shadow&&this.SetShadow(d),d.clearRect(0,0,i,j);for(c=0;c<k;++c){if(!r&&e[c].z<=0){try{this.centreFunc(d,i,j,h,g)}catch(a){alert(a),this.centreFunc=x}r=!0}b&&b.tag==e[c]&&b.PreDraw(d,e[c],h,g)||e[c].Draw(d,h,g),b&&b.tag==e[c]&&b.PostDraw(d)}this.freezeActive&&b?this.Freeze():(this.UnFreeze(),this.drawn=k==this.listLength),this.fixedCallback&&(this.fixedCallback(this,this.fixedCallbackTag),this.fixedCallback=null),n||this.Animate(i,j,p),b&&(b.LastDraw(d),b.tag!=t&&(this.currentAudio&&this.currentAudio!=b.tag&&this.currentAudio.StopAudio(),b.tag.PlayAudio()&&(this.currentAudio=b.tag)),m=this.activeCursor),l.style.cursor=m,this.Tooltip(b,this.taglist[o]),this.audioIcon&&this.ShowAudioIcon()},b.TooltipNone=function(){},b.TooltipNative=function(b,a){b?this.canvas.title=a&&a.title?a.title:'':this.canvas.title=this.ctitle},b.SetTTDiv=function(c,d){var a=this,b=a.ttdiv.style;c!=a.ttdiv.innerHTML&&(b.display='none'),a.ttdiv.innerHTML=c,d&&(d.title=a.ttdiv.innerHTML),b.display=='none'&&!a.tttimer&&(a.tttimer=setTimeout(function(){var c=X(a.canvas.id);b.display='block',b.left=c.x+a.mx+'px',b.top=c.y+a.my+24+'px',a.tttimer=null},a.tooltipDelay))},b.TooltipDiv=function(b,a){b&&a&&a.title?this.SetTTDiv(a.title,a):!b&&this.mx!=-1&&this.my!=-1&&this.ctitle.length?this.SetTTDiv(this.ctitle):this.ttdiv.style.display='none'},b.Transform=function(c,a,b){if(a||b){var d=o(a),e=l(a),f=o(b),g=l(b),h=new m([g,0,f,0,1,0,-f,0,g]),i=new m([1,0,0,0,e,-d,0,d,e]);c.transform=c.transform.mul(h.mul(i))}},b.AnimateFixed=function(){var a,b,c,d,e;return!!(this.fadeIn&&(b=q()-this.startTime,b>=this.fadeIn?(this.fadeIn=0,this.fixedAlpha=1):this.fixedAlpha=b/this.fadeIn),this.fixedAnim)&&(this.fixedAnim.transform||(this.fixedAnim.transform=this.transform),a=this.fixedAnim,b=q()-a.t0,c=a.angle,d,e=this.animTiming(a.t,b),this.transform=a.transform,b>=a.t?(this.fixedCallbackTag=a.tag,this.fixedCallback=a.cb,this.fixedAnim=this.yaw=this.pitch=0):c*=e,d=m.Rotation(c,a.axis),this.transform=this.transform.mul(d),this.fixedAnim!=0)},b.AnimatePosition=function(g,h,f){var a=this,d=a.mx,e=a.my,b,c;!a.frozen&&d>=0&&e>=0&&d<g&&e<h?(b=a.maxSpeed,c=a.reverse?-1:1,a.lx||(a.yaw=(d*2*b/g-b)*c*f),a.ly||(a.pitch=(e*2*b/h-b)*-c*f),a.initial=null):a.initial||(a.frozen&&!a.freezeDecel?a.yaw=a.pitch=0:a.Decel(a)),this.Transform(a,a.pitch,a.yaw)},b.AnimateDrag=function(d,e,c){var a=this,b=100*c*a.maxSpeed/a.max_radius/a.zoom;a.dx||a.dy?(a.lx||(a.yaw=a.dx*b/a.stretchX),a.ly||(a.pitch=a.dy*-b/a.stretchY),a.dx=a.dy=0,a.initial=null):a.initial||a.Decel(a),this.Transform(a,a.pitch,a.yaw)},b.Freeze=function(){this.frozen||(this.preFreeze=[this.yaw,this.pitch],this.frozen=1,this.drawn=0)},b.UnFreeze=function(){this.frozen&&(this.yaw=this.preFreeze[0],this.pitch=this.preFreeze[1],this.frozen=0)},b.Decel=function(a){var b=a.minSpeed,c=p(a.yaw),d=p(a.pitch);!a.lx&&c>b&&(a.yaw=c>a.z0?a.yaw*a.decel:0),!a.ly&&d>b&&(a.pitch=d>a.z0?a.pitch*a.decel:0)},b.Zoom=function(a){this.z2=this.z1*(1/a),this.drawn=0},b.Clicked=function(b){if(this.CheckAudioIcon()){this.ToggleAudio();return}var a=this.active;try{a&&a.tag&&(this.clickToFront===!1||this.clickToFront===null?a.tag.Clicked(b):this.TagToFront(a.tag,this.clickToFront,function(){a.tag.Clicked(b)},!0))}catch(a){}},b.Wheel=function(a){var b=this.zoom+this.zoomStep*(a?1:-1);this.zoom=h(this.zoomMax,g(this.zoomMin,b)),this.Zoom(this.zoom)},b.BeginDrag=function(a){this.down=K(a,this.canvas),a.cancelBubble=!0,a.returnValue=!1,a.preventDefault&&a.preventDefault()},b.Drag=function(e,a){if(this.dragControl&&this.down){var d=this.dragThreshold*this.dragThreshold,b=a.x-this.down.x,c=a.y-this.down.y;(this.dragging||b*b+c*c>d)&&(this.dx=b,this.dy=c,this.dragging=1,this.down=a)}return this.dragging},b.EndDrag=function(){var a=this.dragging;return this.dragging=this.down=null,a};function ah(a){var b=a.targetTouches[0],c=a.targetTouches[1];return E(w(c.pageX-b.pageX,2)+w(c.pageY-b.pageY,2))}b.BeginPinch=function(a){this.pinched=[ah(a),this.zoom],a.preventDefault&&a.preventDefault()},b.Pinch=function(d){var b,c,a=this.pinched;if(!a)return;c=ah(d),b=a[1]*c/a[0],this.zoom=h(this.zoomMax,g(this.zoomMin,b)),this.Zoom(this.zoom)},b.EndPinch=function(a){this.pinched=null},b.Pause=function(){this.paused=!0},b.Resume=function(){this.paused=!1},b.SetSpeed=function(a){this.initial=a,this.yaw=a[0]*this.maxSpeed,this.pitch=a[1]*this.maxSpeed},b.FindTag=function(a){if(!n(a))return null;if(n(a.index)&&(a=a.index),!B(a))return this.taglist[a];var c,d,b;n(a.id)?(c='id',d=a.id):n(a.text)&&(c='innerText',d=a.text);for(b=0;b<this.taglist.length;++b)if(this.taglist[b].a[c]==d)return this.taglist[b]},b.RotateTag=function(a,h,i,j,f,g){var b=a.Calc(this.transform,1),c=new s(b.x,b.y,b.z),d=ay(i,h),e=c.angle(d),k=c.cross(d).unit();e==0?(this.fixedCallbackTag=a,this.fixedCallback=f):this.fixedAnim={angle:-e,axis:k,t:j,t0:q(),cb:f,tag:a,active:g}},b.TagToFront=function(a,b,c,d){this.RotateTag(a,0,0,b,c,d)},b.Volume=function(a){this.audioVolume=a*1},a.Start=function(b,c,d){a.Delete(b),a.tc[b]=new a(b,c,d)};function N(c,b){a.tc[b]&&a.tc[b][c]()}a.Linear=function(a,b){return b/a},a.Smooth=function(a,b){return.5-l(b*Math.PI/a)/2},a.Pause=function(a){N('Pause',a)},a.Resume=function(a){N('Resume',a)},a.Reload=function(a){N('Load',a)},a.Update=function(a){N('Update',a)},a.SetSpeed=function(c,b){return!!(B(b)&&a.tc[c]&&!isNaN(b[0])&&!isNaN(b[1]))&&(a.tc[c].SetSpeed(b),!0)},a.TagToFront=function(c,b){return!!B(b)&&(b.lat=b.lng=0,a.RotateTag(c,b))},a.RotateTag=function(c,b){if(B(b)&&a.tc[c]){isNaN(b.time)&&(b.time=500);var d=a.tc[c].FindTag(b);if(d)return a.tc[c].RotateTag(d,b.lat,b.lng,b.time,b.callback,b.active),!0}return!1},a.Delete=function(b){var d,e;if(j[b])if(e=c.getElementById(b),e)for(d=0;d<j[b].length;++d)am(j[b][d][0],j[b][d][1],e);delete j[b],delete a.tc[b]},a.tc={},a.options={z1:2e4,z2:2e4,z0:2e-4,freezeActive:!1,freezeDecel:!1,activeCursor:'pointer',pulsateTo:1,pulsateTime:3,reverse:!1,depth:.5,maxSpeed:.05,minSpeed:0,decel:.95,interval:20,minBrightness:.1,maxBrightness:1,outlineColour:'#ffff99',outlineThickness:2,outlineOffset:5,outlineMethod:'outline',outlineRadius:0,textColour:'#ff99ff',textHeight:15,textFont:'Helvetica, Arial, sans-serif',shadow:'#000',shadowBlur:0,shadowOffset:[0,0],initial:null,hideTags:!0,zoom:1,weight:!1,weightMode:'size',weightFrom:null,weightSize:1,weightSizeMin:null,weightSizeMax:null,weightGradient:{0:'#f00',0.33:'#ff0',0.66:'#0f0',1:'#00f'},txtOpt:!0,txtScale:2,frontSelect:!1,wheelZoom:!0,zoomMin:.3,zoomMax:3,zoomStep:.05,shape:'sphere',lock:null,tooltip:null,tooltipDelay:300,tooltipClass:'tctooltip',radiusX:1,radiusY:1,radiusZ:1,stretchX:1,stretchY:1,offsetX:0,offsetY:0,shuffleTags:!1,noSelect:!1,noMouse:!1,imageScale:1,paused:!1,dragControl:!1,dragThreshold:4,centreFunc:x,splitWidth:0,animTiming:'Smooth',clickToFront:!1,fadeIn:0,padding:0,bgColour:null,bgRadius:0,bgOutline:null,bgOutlineThickness:0,outlineIncrease:4,textAlign:'centre',textVAlign:'middle',imageMode:null,imagePosition:null,imagePadding:2,imageAlign:'centre',imageVAlign:'middle',noTagsMessage:!0,centreImage:null,pinchZoom:!1,repeatTags:0,minTags:0,imageRadius:0,scrollPause:!1,outlineDash:0,outlineDashSpace:0,outlineDashSpeed:1,activeAudio:'',audioVolume:1,audioIcon:1,audioIconSize:20,audioIconThickness:2,audioIconDark:0,altImage:0};for(r in a.options)a[r]=a.options[r];window.TagCanvas=a,t('load',function(){a.loaded=1},window)})()`\n", "const s = () =>\n  Math.floor((1 + Math.random()) * 0x10000)\n    .toString(16)\n    .substring(1)\n\nexport const guid = () => `${s()}${s()}-${s()}-${s()}-${s()}-${s()}${s()}${s()}`\n", "import React from 'react'\n\nexport const UseInViewport = ({\n  cb,\n  children,\n}: {\n  cb: (isInViewPort: boolean) => void\n  children: JSX.Element | JSX.Element[]\n}) => {\n  const ref = React.useRef(null) as any\n\n  React.useEffect(() => {\n    // https://developer.mozilla.org/en-US/docs/Web/API/IntersectionObserver/IntersectionObserver\n    const options = {\n      root: null,\n      rootMargin: '0px',\n      threshold: 0.1,\n    }\n\n    const wrappedCb: IntersectionObserverCallback = entries => {\n      cb(entries.some(e => e.isIntersecting))\n    }\n\n    const observer = new IntersectionObserver(wrappedCb, options)\n\n    if (ref?.current) {\n      observer.observe(ref.current)\n    }\n\n    return () => {\n      observer.disconnect()\n    }\n  }, [cb])\n\n  return <div ref={ref}>{children}</div>\n}\n", "import React from 'react'\nimport {tagCanvasString} from '../lib/tag_canvas_string'\nimport {guid} from '../utils/guid'\nimport {UseInViewport} from '../utils/use_in_viewport'\nimport {ICloud} from '../types/cloud'\n\nlet isScriptLoaded = false\n\nconst tr = (fn: () => void) => {\n  try {\n    fn()\n  } catch (e) {\n    try {\n      fn()\n    } catch (e) {}\n  }\n}\n\nconst CloudWrapped = ({\n  options = {},\n  containerProps = {},\n  canvasProps = {},\n  children,\n  id = guid(),\n}: ICloud) => {\n  const state = React.useRef({\n    canvasContainerId: 'canvas-container-' + id,\n    canvasId: 'canvas-' + id,\n    hasStarted: false,\n  }).current\n  const [mounted, setMounted] = React.useState(false)\n\n  React.useEffect(() => {\n    setMounted(true)\n    return () =>\n      tr(() => {\n        eval(`TagCanvas.Delete('${state.canvasId}')`)\n      })\n  }, [])\n\n  const supportsTouch =\n    typeof window !== 'undefined'\n      ? 'ontouchstart' in window || navigator.maxTouchPoints\n      : false\n  const ops = JSON.stringify({\n    dragControl: supportsTouch ? true : false,\n    maxSpeed: supportsTouch ? 0.01 : 0.05,\n    textFont: null,\n    textColour: null,\n    ...options,\n  })\n\n  // it will not load canvas animations when its outside the viewport\n  const onVisibilityChange = (isVisible: boolean) =>\n    tr(() => {\n      if (isVisible && mounted) {\n        if (!isScriptLoaded) {\n          eval(tagCanvasString)\n          isScriptLoaded = true\n        }\n\n        if (state.hasStarted) {\n          eval(`TagCanvas.Resume('${state.canvasId}')`)\n        } else {\n          try {\n            eval(`TagCanvas.Start('${state.canvasId}', null, ${ops})`)\n            state.hasStarted = true\n          } catch (e) {\n            const el = document.getElementById(state.canvasContainerId)\n\n            if (el) {\n              el.style.display = 'none'\n            }\n\n            throw e\n          }\n        }\n      } else {\n        if (state.hasStarted) {\n          eval(`TagCanvas.Pause('${state.canvasId}')`)\n        }\n      }\n    })\n\n  return (\n    <UseInViewport cb={onVisibilityChange}>\n      <div id={state.canvasContainerId} {...containerProps}>\n        <canvas\n          id={state.canvasId}\n          style={{width: '100%', maxWidth: '70vh'}}\n          width={1000}\n          height={1000}\n          {...canvasProps}\n        >\n          {children}\n        </canvas>\n      </div>\n    </UseInViewport>\n  )\n}\n\nexport const Cloud = (props: ICloud) => {\n  return <CloudWrapped {...props} key={guid()} />\n}\n", "export const addHash = (color: string) =>\n  color[0] === '#' ? color : `#${color}`\n", "import React from 'react'\nimport {hex2contrast, hex2rgb} from '@csstools/convert-colors'\nimport {guid} from '../utils/guid'\nimport {addHash} from '../utils/add_hash'\n\n/**\n * Used to create a tag for the Cloud component\n * @param options.aProps Attributes passed to the underlying anchor element\n * @param options.bgHex The string hex of the background the icon will be rendered on. Ex: '#fff'. Used to determine if the min contrast ratio for the icons default color will be met\n * @param options.fallbackHex The color of the icon if the minContrastRatio is not met Ex: '#000'\n * @param options.icon The simple icon object you would like to render. Ex: import icon from \"simple-icons/icons/javascript\";\n * @param options.imgProps Attributes passes to the underlying img element\n * @param options.minContrastRatio 0 - 21 The min contrast ratio between icon and bgHex before the fallbackHex will be used for the icon color\n * @param options.size The size in px of the icon\n * @returns A component that can be rendered as a tag inside the Cloud component\n */\nexport const renderSimpleIcon = ({\n  aProps = {},\n  bgHex = '#fff',\n  fallbackHex = '#000',\n  icon,\n  imgProps = {},\n  minContrastRatio = 1,\n  size = 42,\n}: {\n  aProps?: React.DetailedHTMLProps<\n    React.AllHTMLAttributes<HTMLAnchorElement>,\n    HTMLAnchorElement\n  >\n  bgHex?: string\n  fallbackHex?: string\n  icon: any\n  imgProps?: React.DetailedHTMLProps<\n    React.ImgHTMLAttributes<HTMLImageElement>,\n    HTMLImageElement\n  >\n  minContrastRatio?: number\n  size?: number\n}) => {\n  const originalHex = addHash(icon.hex)\n  const bgHexHash = addHash(bgHex)\n  const fallbackHexHash = addHash(fallbackHex)\n  const isAccessibleColor =\n    hex2contrast(bgHexHash, originalHex) > minContrastRatio\n  const rgb = isAccessibleColor\n    ? hex2rgb(originalHex)\n    : hex2rgb(fallbackHexHash)\n  const [r, g, b] = rgb.map((percent: number) =>\n    Math.round((percent / 100) * 255)\n  )\n  const imgSrc = `data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" style=\"fill: rgb(${r}, ${g}, ${b});\" viewBox=\"0 0 24 24\" height=\"${size}px\" width=\"${size}px\"> <title>${icon.title}</title> <path d=\"${icon.path}\"></path> </svg>`\n\n  const a = {\n    key: guid(),\n    title: icon.title,\n    style: {cursor: 'pointer'},\n    ...aProps,\n  }\n\n  const i = {\n    height: size,\n    width: size,\n    alt: icon.title,\n    src: imgSrc,\n    ...imgProps,\n  }\n\n  return (\n    <a {...a}>\n      <img {...i} />\n    </a>\n  )\n}\n", "/**\n * Copyright (c) 2014-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar runtime = (function (exports) {\n  \"use strict\";\n\n  var Op = Object.prototype;\n  var hasOwn = Op.hasOwnProperty;\n  var undefined; // More compressible than void 0.\n  var $Symbol = typeof Symbol === \"function\" ? Symbol : {};\n  var iteratorSymbol = $Symbol.iterator || \"@@iterator\";\n  var asyncIteratorSymbol = $Symbol.asyncIterator || \"@@asyncIterator\";\n  var toStringTagSymbol = $Symbol.toStringTag || \"@@toStringTag\";\n\n  function define(obj, key, value) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n    return obj[key];\n  }\n  try {\n    // IE 8 has a broken Object.defineProperty that only works on DOM objects.\n    define({}, \"\");\n  } catch (err) {\n    define = function(obj, key, value) {\n      return obj[key] = value;\n    };\n  }\n\n  function wrap(innerFn, outerFn, self, tryLocsList) {\n    // If outerFn provided and outerFn.prototype is a Generator, then outerFn.prototype instanceof Generator.\n    var protoGenerator = outerFn && outerFn.prototype instanceof Generator ? outerFn : Generator;\n    var generator = Object.create(protoGenerator.prototype);\n    var context = new Context(tryLocsList || []);\n\n    // The ._invoke method unifies the implementations of the .next,\n    // .throw, and .return methods.\n    generator._invoke = makeInvokeMethod(innerFn, self, context);\n\n    return generator;\n  }\n  exports.wrap = wrap;\n\n  // Try/catch helper to minimize deoptimizations. Returns a completion\n  // record like context.tryEntries[i].completion. This interface could\n  // have been (and was previously) designed to take a closure to be\n  // invoked without arguments, but in all the cases we care about we\n  // already have an existing method we want to call, so there's no need\n  // to create a new function object. We can even get away with assuming\n  // the method takes exactly one argument, since that happens to be true\n  // in every case, so we don't have to touch the arguments object. The\n  // only additional allocation required is the completion record, which\n  // has a stable shape and so hopefully should be cheap to allocate.\n  function tryCatch(fn, obj, arg) {\n    try {\n      return { type: \"normal\", arg: fn.call(obj, arg) };\n    } catch (err) {\n      return { type: \"throw\", arg: err };\n    }\n  }\n\n  var GenStateSuspendedStart = \"suspendedStart\";\n  var GenStateSuspendedYield = \"suspendedYield\";\n  var GenStateExecuting = \"executing\";\n  var GenStateCompleted = \"completed\";\n\n  // Returning this object from the innerFn has the same effect as\n  // breaking out of the dispatch switch statement.\n  var ContinueSentinel = {};\n\n  // Dummy constructor functions that we use as the .constructor and\n  // .constructor.prototype properties for functions that return Generator\n  // objects. For full spec compliance, you may wish to configure your\n  // minifier not to mangle the names of these two functions.\n  function Generator() {}\n  function GeneratorFunction() {}\n  function GeneratorFunctionPrototype() {}\n\n  // This is a polyfill for %IteratorPrototype% for environments that\n  // don't natively support it.\n  var IteratorPrototype = {};\n  define(IteratorPrototype, iteratorSymbol, function () {\n    return this;\n  });\n\n  var getProto = Object.getPrototypeOf;\n  var NativeIteratorPrototype = getProto && getProto(getProto(values([])));\n  if (NativeIteratorPrototype &&\n      NativeIteratorPrototype !== Op &&\n      hasOwn.call(NativeIteratorPrototype, iteratorSymbol)) {\n    // This environment has a native %IteratorPrototype%; use it instead\n    // of the polyfill.\n    IteratorPrototype = NativeIteratorPrototype;\n  }\n\n  var Gp = GeneratorFunctionPrototype.prototype =\n    Generator.prototype = Object.create(IteratorPrototype);\n  GeneratorFunction.prototype = GeneratorFunctionPrototype;\n  define(Gp, \"constructor\", GeneratorFunctionPrototype);\n  define(GeneratorFunctionPrototype, \"constructor\", GeneratorFunction);\n  GeneratorFunction.displayName = define(\n    GeneratorFunctionPrototype,\n    toStringTagSymbol,\n    \"GeneratorFunction\"\n  );\n\n  // Helper for defining the .next, .throw, and .return methods of the\n  // Iterator interface in terms of a single ._invoke method.\n  function defineIteratorMethods(prototype) {\n    [\"next\", \"throw\", \"return\"].forEach(function(method) {\n      define(prototype, method, function(arg) {\n        return this._invoke(method, arg);\n      });\n    });\n  }\n\n  exports.isGeneratorFunction = function(genFun) {\n    var ctor = typeof genFun === \"function\" && genFun.constructor;\n    return ctor\n      ? ctor === GeneratorFunction ||\n        // For the native GeneratorFunction constructor, the best we can\n        // do is to check its .name property.\n        (ctor.displayName || ctor.name) === \"GeneratorFunction\"\n      : false;\n  };\n\n  exports.mark = function(genFun) {\n    if (Object.setPrototypeOf) {\n      Object.setPrototypeOf(genFun, GeneratorFunctionPrototype);\n    } else {\n      genFun.__proto__ = GeneratorFunctionPrototype;\n      define(genFun, toStringTagSymbol, \"GeneratorFunction\");\n    }\n    genFun.prototype = Object.create(Gp);\n    return genFun;\n  };\n\n  // Within the body of any async function, `await x` is transformed to\n  // `yield regeneratorRuntime.awrap(x)`, so that the runtime can test\n  // `hasOwn.call(value, \"__await\")` to determine if the yielded value is\n  // meant to be awaited.\n  exports.awrap = function(arg) {\n    return { __await: arg };\n  };\n\n  function AsyncIterator(generator, PromiseImpl) {\n    function invoke(method, arg, resolve, reject) {\n      var record = tryCatch(generator[method], generator, arg);\n      if (record.type === \"throw\") {\n        reject(record.arg);\n      } else {\n        var result = record.arg;\n        var value = result.value;\n        if (value &&\n            typeof value === \"object\" &&\n            hasOwn.call(value, \"__await\")) {\n          return PromiseImpl.resolve(value.__await).then(function(value) {\n            invoke(\"next\", value, resolve, reject);\n          }, function(err) {\n            invoke(\"throw\", err, resolve, reject);\n          });\n        }\n\n        return PromiseImpl.resolve(value).then(function(unwrapped) {\n          // When a yielded Promise is resolved, its final value becomes\n          // the .value of the Promise<{value,done}> result for the\n          // current iteration.\n          result.value = unwrapped;\n          resolve(result);\n        }, function(error) {\n          // If a rejected Promise was yielded, throw the rejection back\n          // into the async generator function so it can be handled there.\n          return invoke(\"throw\", error, resolve, reject);\n        });\n      }\n    }\n\n    var previousPromise;\n\n    function enqueue(method, arg) {\n      function callInvokeWithMethodAndArg() {\n        return new PromiseImpl(function(resolve, reject) {\n          invoke(method, arg, resolve, reject);\n        });\n      }\n\n      return previousPromise =\n        // If enqueue has been called before, then we want to wait until\n        // all previous Promises have been resolved before calling invoke,\n        // so that results are always delivered in the correct order. If\n        // enqueue has not been called before, then it is important to\n        // call invoke immediately, without waiting on a callback to fire,\n        // so that the async generator function has the opportunity to do\n        // any necessary setup in a predictable way. This predictability\n        // is why the Promise constructor synchronously invokes its\n        // executor callback, and why async functions synchronously\n        // execute code before the first await. Since we implement simple\n        // async functions in terms of async generators, it is especially\n        // important to get this right, even though it requires care.\n        previousPromise ? previousPromise.then(\n          callInvokeWithMethodAndArg,\n          // Avoid propagating failures to Promises returned by later\n          // invocations of the iterator.\n          callInvokeWithMethodAndArg\n        ) : callInvokeWithMethodAndArg();\n    }\n\n    // Define the unified helper method that is used to implement .next,\n    // .throw, and .return (see defineIteratorMethods).\n    this._invoke = enqueue;\n  }\n\n  defineIteratorMethods(AsyncIterator.prototype);\n  define(AsyncIterator.prototype, asyncIteratorSymbol, function () {\n    return this;\n  });\n  exports.AsyncIterator = AsyncIterator;\n\n  // Note that simple async functions are implemented on top of\n  // AsyncIterator objects; they just return a Promise for the value of\n  // the final result produced by the iterator.\n  exports.async = function(innerFn, outerFn, self, tryLocsList, PromiseImpl) {\n    if (PromiseImpl === void 0) PromiseImpl = Promise;\n\n    var iter = new AsyncIterator(\n      wrap(innerFn, outerFn, self, tryLocsList),\n      PromiseImpl\n    );\n\n    return exports.isGeneratorFunction(outerFn)\n      ? iter // If outerFn is a generator, return the full iterator.\n      : iter.next().then(function(result) {\n          return result.done ? result.value : iter.next();\n        });\n  };\n\n  function makeInvokeMethod(innerFn, self, context) {\n    var state = GenStateSuspendedStart;\n\n    return function invoke(method, arg) {\n      if (state === GenStateExecuting) {\n        throw new Error(\"Generator is already running\");\n      }\n\n      if (state === GenStateCompleted) {\n        if (method === \"throw\") {\n          throw arg;\n        }\n\n        // Be forgiving, per 25.3.3.3.3 of the spec:\n        // https://people.mozilla.org/~jorendorff/es6-draft.html#sec-generatorresume\n        return doneResult();\n      }\n\n      context.method = method;\n      context.arg = arg;\n\n      while (true) {\n        var delegate = context.delegate;\n        if (delegate) {\n          var delegateResult = maybeInvokeDelegate(delegate, context);\n          if (delegateResult) {\n            if (delegateResult === ContinueSentinel) continue;\n            return delegateResult;\n          }\n        }\n\n        if (context.method === \"next\") {\n          // Setting context._sent for legacy support of Babel's\n          // function.sent implementation.\n          context.sent = context._sent = context.arg;\n\n        } else if (context.method === \"throw\") {\n          if (state === GenStateSuspendedStart) {\n            state = GenStateCompleted;\n            throw context.arg;\n          }\n\n          context.dispatchException(context.arg);\n\n        } else if (context.method === \"return\") {\n          context.abrupt(\"return\", context.arg);\n        }\n\n        state = GenStateExecuting;\n\n        var record = tryCatch(innerFn, self, context);\n        if (record.type === \"normal\") {\n          // If an exception is thrown from innerFn, we leave state ===\n          // GenStateExecuting and loop back for another invocation.\n          state = context.done\n            ? GenStateCompleted\n            : GenStateSuspendedYield;\n\n          if (record.arg === ContinueSentinel) {\n            continue;\n          }\n\n          return {\n            value: record.arg,\n            done: context.done\n          };\n\n        } else if (record.type === \"throw\") {\n          state = GenStateCompleted;\n          // Dispatch the exception by looping back around to the\n          // context.dispatchException(context.arg) call above.\n          context.method = \"throw\";\n          context.arg = record.arg;\n        }\n      }\n    };\n  }\n\n  // Call delegate.iterator[context.method](context.arg) and handle the\n  // result, either by returning a { value, done } result from the\n  // delegate iterator, or by modifying context.method and context.arg,\n  // setting context.delegate to null, and returning the ContinueSentinel.\n  function maybeInvokeDelegate(delegate, context) {\n    var method = delegate.iterator[context.method];\n    if (method === undefined) {\n      // A .throw or .return when the delegate iterator has no .throw\n      // method always terminates the yield* loop.\n      context.delegate = null;\n\n      if (context.method === \"throw\") {\n        // Note: [\"return\"] must be used for ES3 parsing compatibility.\n        if (delegate.iterator[\"return\"]) {\n          // If the delegate iterator has a return method, give it a\n          // chance to clean up.\n          context.method = \"return\";\n          context.arg = undefined;\n          maybeInvokeDelegate(delegate, context);\n\n          if (context.method === \"throw\") {\n            // If maybeInvokeDelegate(context) changed context.method from\n            // \"return\" to \"throw\", let that override the TypeError below.\n            return ContinueSentinel;\n          }\n        }\n\n        context.method = \"throw\";\n        context.arg = new TypeError(\n          \"The iterator does not provide a 'throw' method\");\n      }\n\n      return ContinueSentinel;\n    }\n\n    var record = tryCatch(method, delegate.iterator, context.arg);\n\n    if (record.type === \"throw\") {\n      context.method = \"throw\";\n      context.arg = record.arg;\n      context.delegate = null;\n      return ContinueSentinel;\n    }\n\n    var info = record.arg;\n\n    if (! info) {\n      context.method = \"throw\";\n      context.arg = new TypeError(\"iterator result is not an object\");\n      context.delegate = null;\n      return ContinueSentinel;\n    }\n\n    if (info.done) {\n      // Assign the result of the finished delegate to the temporary\n      // variable specified by delegate.resultName (see delegateYield).\n      context[delegate.resultName] = info.value;\n\n      // Resume execution at the desired location (see delegateYield).\n      context.next = delegate.nextLoc;\n\n      // If context.method was \"throw\" but the delegate handled the\n      // exception, let the outer generator proceed normally. If\n      // context.method was \"next\", forget context.arg since it has been\n      // \"consumed\" by the delegate iterator. If context.method was\n      // \"return\", allow the original .return call to continue in the\n      // outer generator.\n      if (context.method !== \"return\") {\n        context.method = \"next\";\n        context.arg = undefined;\n      }\n\n    } else {\n      // Re-yield the result returned by the delegate method.\n      return info;\n    }\n\n    // The delegate iterator is finished, so forget it and continue with\n    // the outer generator.\n    context.delegate = null;\n    return ContinueSentinel;\n  }\n\n  // Define Generator.prototype.{next,throw,return} in terms of the\n  // unified ._invoke helper method.\n  defineIteratorMethods(Gp);\n\n  define(Gp, toStringTagSymbol, \"Generator\");\n\n  // A Generator should always return itself as the iterator object when the\n  // @@iterator function is called on it. Some browsers' implementations of the\n  // iterator prototype chain incorrectly implement this, causing the Generator\n  // object to not be returned from this call. This ensures that doesn't happen.\n  // See https://github.com/facebook/regenerator/issues/274 for more details.\n  define(Gp, iteratorSymbol, function() {\n    return this;\n  });\n\n  define(Gp, \"toString\", function() {\n    return \"[object Generator]\";\n  });\n\n  function pushTryEntry(locs) {\n    var entry = { tryLoc: locs[0] };\n\n    if (1 in locs) {\n      entry.catchLoc = locs[1];\n    }\n\n    if (2 in locs) {\n      entry.finallyLoc = locs[2];\n      entry.afterLoc = locs[3];\n    }\n\n    this.tryEntries.push(entry);\n  }\n\n  function resetTryEntry(entry) {\n    var record = entry.completion || {};\n    record.type = \"normal\";\n    delete record.arg;\n    entry.completion = record;\n  }\n\n  function Context(tryLocsList) {\n    // The root entry object (effectively a try statement without a catch\n    // or a finally block) gives us a place to store values thrown from\n    // locations where there is no enclosing try statement.\n    this.tryEntries = [{ tryLoc: \"root\" }];\n    tryLocsList.forEach(pushTryEntry, this);\n    this.reset(true);\n  }\n\n  exports.keys = function(object) {\n    var keys = [];\n    for (var key in object) {\n      keys.push(key);\n    }\n    keys.reverse();\n\n    // Rather than returning an object with a next method, we keep\n    // things simple and return the next function itself.\n    return function next() {\n      while (keys.length) {\n        var key = keys.pop();\n        if (key in object) {\n          next.value = key;\n          next.done = false;\n          return next;\n        }\n      }\n\n      // To avoid creating an additional object, we just hang the .value\n      // and .done properties off the next function object itself. This\n      // also ensures that the minifier will not anonymize the function.\n      next.done = true;\n      return next;\n    };\n  };\n\n  function values(iterable) {\n    if (iterable) {\n      var iteratorMethod = iterable[iteratorSymbol];\n      if (iteratorMethod) {\n        return iteratorMethod.call(iterable);\n      }\n\n      if (typeof iterable.next === \"function\") {\n        return iterable;\n      }\n\n      if (!isNaN(iterable.length)) {\n        var i = -1, next = function next() {\n          while (++i < iterable.length) {\n            if (hasOwn.call(iterable, i)) {\n              next.value = iterable[i];\n              next.done = false;\n              return next;\n            }\n          }\n\n          next.value = undefined;\n          next.done = true;\n\n          return next;\n        };\n\n        return next.next = next;\n      }\n    }\n\n    // Return an iterator with no values.\n    return { next: doneResult };\n  }\n  exports.values = values;\n\n  function doneResult() {\n    return { value: undefined, done: true };\n  }\n\n  Context.prototype = {\n    constructor: Context,\n\n    reset: function(skipTempReset) {\n      this.prev = 0;\n      this.next = 0;\n      // Resetting context._sent for legacy support of Babel's\n      // function.sent implementation.\n      this.sent = this._sent = undefined;\n      this.done = false;\n      this.delegate = null;\n\n      this.method = \"next\";\n      this.arg = undefined;\n\n      this.tryEntries.forEach(resetTryEntry);\n\n      if (!skipTempReset) {\n        for (var name in this) {\n          // Not sure about the optimal order of these conditions:\n          if (name.charAt(0) === \"t\" &&\n              hasOwn.call(this, name) &&\n              !isNaN(+name.slice(1))) {\n            this[name] = undefined;\n          }\n        }\n      }\n    },\n\n    stop: function() {\n      this.done = true;\n\n      var rootEntry = this.tryEntries[0];\n      var rootRecord = rootEntry.completion;\n      if (rootRecord.type === \"throw\") {\n        throw rootRecord.arg;\n      }\n\n      return this.rval;\n    },\n\n    dispatchException: function(exception) {\n      if (this.done) {\n        throw exception;\n      }\n\n      var context = this;\n      function handle(loc, caught) {\n        record.type = \"throw\";\n        record.arg = exception;\n        context.next = loc;\n\n        if (caught) {\n          // If the dispatched exception was caught by a catch block,\n          // then let that catch block handle the exception normally.\n          context.method = \"next\";\n          context.arg = undefined;\n        }\n\n        return !! caught;\n      }\n\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        var record = entry.completion;\n\n        if (entry.tryLoc === \"root\") {\n          // Exception thrown outside of any try block that could handle\n          // it, so set the completion value of the entire function to\n          // throw the exception.\n          return handle(\"end\");\n        }\n\n        if (entry.tryLoc <= this.prev) {\n          var hasCatch = hasOwn.call(entry, \"catchLoc\");\n          var hasFinally = hasOwn.call(entry, \"finallyLoc\");\n\n          if (hasCatch && hasFinally) {\n            if (this.prev < entry.catchLoc) {\n              return handle(entry.catchLoc, true);\n            } else if (this.prev < entry.finallyLoc) {\n              return handle(entry.finallyLoc);\n            }\n\n          } else if (hasCatch) {\n            if (this.prev < entry.catchLoc) {\n              return handle(entry.catchLoc, true);\n            }\n\n          } else if (hasFinally) {\n            if (this.prev < entry.finallyLoc) {\n              return handle(entry.finallyLoc);\n            }\n\n          } else {\n            throw new Error(\"try statement without catch or finally\");\n          }\n        }\n      }\n    },\n\n    abrupt: function(type, arg) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc <= this.prev &&\n            hasOwn.call(entry, \"finallyLoc\") &&\n            this.prev < entry.finallyLoc) {\n          var finallyEntry = entry;\n          break;\n        }\n      }\n\n      if (finallyEntry &&\n          (type === \"break\" ||\n           type === \"continue\") &&\n          finallyEntry.tryLoc <= arg &&\n          arg <= finallyEntry.finallyLoc) {\n        // Ignore the finally entry if control is not jumping to a\n        // location outside the try/catch block.\n        finallyEntry = null;\n      }\n\n      var record = finallyEntry ? finallyEntry.completion : {};\n      record.type = type;\n      record.arg = arg;\n\n      if (finallyEntry) {\n        this.method = \"next\";\n        this.next = finallyEntry.finallyLoc;\n        return ContinueSentinel;\n      }\n\n      return this.complete(record);\n    },\n\n    complete: function(record, afterLoc) {\n      if (record.type === \"throw\") {\n        throw record.arg;\n      }\n\n      if (record.type === \"break\" ||\n          record.type === \"continue\") {\n        this.next = record.arg;\n      } else if (record.type === \"return\") {\n        this.rval = this.arg = record.arg;\n        this.method = \"return\";\n        this.next = \"end\";\n      } else if (record.type === \"normal\" && afterLoc) {\n        this.next = afterLoc;\n      }\n\n      return ContinueSentinel;\n    },\n\n    finish: function(finallyLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.finallyLoc === finallyLoc) {\n          this.complete(entry.completion, entry.afterLoc);\n          resetTryEntry(entry);\n          return ContinueSentinel;\n        }\n      }\n    },\n\n    \"catch\": function(tryLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc === tryLoc) {\n          var record = entry.completion;\n          if (record.type === \"throw\") {\n            var thrown = record.arg;\n            resetTryEntry(entry);\n          }\n          return thrown;\n        }\n      }\n\n      // The context.catch method must only be called with a location\n      // argument that corresponds to a known catch block.\n      throw new Error(\"illegal catch attempt\");\n    },\n\n    delegateYield: function(iterable, resultName, nextLoc) {\n      this.delegate = {\n        iterator: values(iterable),\n        resultName: resultName,\n        nextLoc: nextLoc\n      };\n\n      if (this.method === \"next\") {\n        // Deliberately forget the last sent value so that we don't\n        // accidentally pass it on to the delegate.\n        this.arg = undefined;\n      }\n\n      return ContinueSentinel;\n    }\n  };\n\n  // Regardless of whether this script is executing as a CommonJS module\n  // or not, return the runtime object so that we can declare the variable\n  // regeneratorRuntime in the outer scope, which allows this module to be\n  // injected easily by `bin/regenerator --include-runtime script.js`.\n  return exports;\n\n}(\n  // If this script is executing as a CommonJS module, use module.exports\n  // as the regeneratorRuntime namespace. Otherwise create a new empty\n  // object. Either way, the resulting object will be used to initialize\n  // the regeneratorRuntime variable at the top of this file.\n  typeof module === \"object\" ? module.exports : {}\n));\n\ntry {\n  regeneratorRuntime = runtime;\n} catch (accidentalStrictMode) {\n  // This module should not be running in strict mode, so the above\n  // assignment should always work unless something is misconfigured. Just\n  // in case runtime.js accidentally runs in strict mode, in modern engines\n  // we can explicitly access globalThis. In older engines we can escape\n  // strict mode using a global Function call. This could conceivably fail\n  // if a Content Security Policy forbids using Function, but in that case\n  // the proper solution is to fix the accidental strict mode problem. If\n  // you've misconfigured your bundler to force strict mode and applied a\n  // CSP to forbid Function, and you're not willing to fix either of those\n  // problems, please detail your unique predicament in a GitHub issue.\n  if (typeof globalThis === \"object\") {\n    globalThis.regeneratorRuntime = runtime;\n  } else {\n    Function(\"r\", \"regeneratorRuntime = r\")(runtime);\n  }\n}\n", "export const svgToPath = (svg: string) => {\n  const p0 = svg.indexOf('d=\"')\n  const p1 = svg.indexOf('\"', p0 + 3)\n  return svg.substring(p0 + 3, p1)\n}\n", "import {svgToPath} from './svg_to_path'\n\nconst url = 'https://cdn.jsdelivr.net/npm/simple-icons@latest/icons/'\nconst cache: Record<string, string> = {}\nconst fallback = ''\n\nconst getSlugPath = async (slug: string) => {\n  if (!cache[slug]) {\n    try {\n      const urlSlug = `${url}${slug}.svg`\n      const res = await fetch(urlSlug, {cache: 'force-cache'})\n      const text = await res.text()\n      const path = svgToPath(text)\n      cache[slug] = path\n    } catch (e) {}\n  }\n\n  if (cache[slug]) {\n    return cache[slug]\n  }\n\n  return fallback\n}\n\nexport const getSlugsPath = async (slugs: string[]) => {\n  const map = {} as any\n  slugs.forEach(s => {\n    map[s] = s\n  })\n  await Promise.all(Object.keys(map).map(getSlugPath))\n  return slugs.map(slug => ({\n    slug,\n    path: cache[slug] ?? fallback,\n  }))\n}\n", "// https://github.com/simple-icons/simple-icons/blob/develop/scripts/utils.js\nconst TITLE_TO_SLUG_REPLACEMENTS: Record<string, string> = {\n  '+': 'plus',\n  '.': 'dot',\n  '&': 'and',\n  đ: 'd',\n  ħ: 'h',\n  ı: 'i',\n  ĸ: 'k',\n  ŀ: 'l',\n  ł: 'l',\n  ß: 'ss',\n  ŧ: 't',\n}\n\nconst TITLE_TO_SLUG_CHARS_REGEX = RegExp(\n  `[${Object.keys(TITLE_TO_SLUG_REPLACEMENTS).join('')}]`,\n  'g'\n)\n\nconst TITLE_TO_SLUG_RANGE_REGEX = /[^a-z0-9]/g\n\nexport const getSlug = ({title}: {title: string}) =>\n  title\n    .toLowerCase()\n    .replace(\n      TITLE_TO_SLUG_CHARS_REGEX,\n      char => TITLE_TO_SLUG_REPLACEMENTS[char]\n    )\n    .normalize('NFD')\n    .replace(TITLE_TO_SLUG_RANGE_REGEX, '')\n", "import {addHash} from './add_hash'\nimport {getSlug} from './get_slug'\n\nconst url =\n  'https://raw.githubusercontent.com/simple-icons/simple-icons/develop/_data/simple-icons.json'\nlet cache:\n  | Record<string, {title: string; hex: string; slug: string}>\n  | undefined\nconst fallback = '#000'\n\ninterface Icon {\n  hex?: string\n  title: string\n  source: string\n}\n\nconst primeCache = async () => {\n  if (!cache) {\n    try {\n      const res = await fetch(url, {cache: 'force-cache'})\n      const json = await res.json()\n      cache = {}\n      json.icons.forEach((icon: Icon) => {\n        const iconSlug = getSlug({title: icon.title})\n        cache![iconSlug] = {\n          hex: addHash(icon.hex ?? fallback),\n          title: icon.title,\n          slug: iconSlug,\n        }\n      })\n    } catch (e) {}\n  }\n}\n\nexport const getSlugHexs = async (slugs: string[]) => {\n  await primeCache()\n  return {\n    hexs: slugs.map(slug => ({\n      slug,\n      hex: cache ? cache[slug]?.hex ?? fallback : fallback,\n      title: cache ? cache[slug]?.title ?? 'icon' : 'icon',\n    })),\n    cache,\n  }\n}\n", "import {SimpleIcon} from '../types/simple_icon'\nimport {getSlugsPath} from './get_slugs_path'\nimport {getSlugHexs} from './get_slug_hexs'\n\nexport const fetchSimpleIcons = async ({slugs}: {slugs: string[]}) => {\n  const [paths, {hexs, cache}] = await Promise.all([\n    getSlugsPath(slugs),\n    getSlugHexs(slugs),\n  ])\n  const map = {} as any\n  hexs.forEach(hex => {\n    map[hex.slug] = hex\n  })\n  paths.forEach(path => {\n    map[path.slug].path = path.path\n  })\n  slugs.forEach(s => {\n    const o = map[s]\n    if (!o.hex || !o.path) {\n      if (process.env.NODE_ENV !== 'production') {\n        console.error(\n          `'react-icon-cloud/fetchSimpleIcons': the response of ${o.slug} was malformed and it will be ignored.`\n        )\n      }\n      delete map[s]\n    }\n  })\n  return {\n    simpleIcons: map as Record<string, SimpleIcon>,\n    allIcon: cache!,\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiIO,IAAMA,IAAY;AAAlB,IAyBMC,IAAMC,KAAKD;AAzBjB,IA0BME,IAAMD,KAAKC;AA1BjB,IA4BMC,IAAMC,KAAKD;AA5BjB,IAgCME,IAAUC,EAAI,GAAG,CAAA,IAAKA,EAAI,IAAI,CAAA;AAhCpC,IAiCMC,IAAQD,EAAI,IAAI,CAAA,IAAKA,EAAI,GAAG,CAAA;ASrJ1B,SAASE,EAAaC,IAAMC,GAAAA;AAAAA,ST0EpC,SAA4BC,IAAoBC,IAAAA;AAAAA,UAEhDC,IAAKC,EAAIH,IAAoBC,EAAAA,GAG7BG,IAAKC,EAAIL,IAAoBC,EAAAA;AAAAA,YAE3BC,IAAKI,IAAY,OAAOA,MAAcF,IAAKE,IAAY,OAAOA;EAAAA,EShFnDC,EAAAA,GAAiBT,EAAAA,GACjBS,EAAAA,GAAiBR,CAAAA,CAAAA;AAAAA;AAkBrC,SAAgBQ,EAAcC,IAAMC,GAAMC,GAAAA;AAAAA,UACjCC,EAAcH,EAAAA,IAAQI,IAAeD,EAAcF,CAAAA,IAAQI,IAAeF,EAAcD,CAAAA,IAAQI,KAAgBR;AAAAA;AAIzH,IAAMK,IAAgBI,CAAAA,OAAKA,MAAK,QAAQA,KAAIC,IAAOC,EAAYF,EAAAA;AAA/D,IACME,IAAcF,CAAAA,OAAKG,GAAKH,KAAI,OAAO,OAAO,GAAA;AADhD,IAEMC,IAAO;AAFb,IAKMJ,IAAe,SAASN;AAL9B,IAMMO,IAAe,SAASP;AAN9B,IAOMQ,IAAe,SAASR;AChCvB,SAASa,EAAQC,GAAAA;AAAAA,QAAAA,IAAAA,EAEgBA,EAAIC,MAAMC,EAAAA,KAAkB,CAAA,GAAA,CAAA,GAA1DC,IAAAA,EAAAA,CAAAA,GAAGC,IAAAA,EAAAA,CAAAA,GAAGC,IAAAA,EAAAA,CAAAA,GAAGC,KAAAA,EAAAA,CAAAA,GAAGC,IAAAA,EAAAA,CAAAA,GAAIC,IAAAA,EAAAA,CAAAA,GAAIC,IAAAA,EAAAA,CAAAA,GAAIC,KAAAA,EAAAA,CAAAA;AAAAA,MAAAA,WAE7BH,KAAAA,WAAoBJ,GAAiB;AAAA,WAMjC,CAAA,WALOI,IAAmBI,SAASJ,GAAI,EAAA,IAAMI,SAASR,IAAIA,GAAG,EAAA,GAAA,WACtDK,IAAmBG,SAASH,GAAI,EAAA,IAAMG,SAASP,IAAIA,GAAG,EAAA,GAAA,WACtDK,IAAmBE,SAASF,GAAI,EAAA,IAAME,SAASN,IAAIA,GAAG,EAAA,GAAA,WACtDK,KAAmBC,SAASD,IAAI,EAAA,IAAA,WAAMJ,KAAkBK,SAASL,KAAIA,IAAG,EAAA,IAAM,GAAA,EAE3DM,IAAIC,CAAAA,OAAS,MAAJA,KAAU,GAAA;EAAA;AAAA;AAqBtD,IAAMC,KAAgB;AG+xBtB,SAASC,GAAaC,IAAMC,GAAAA;AAAAA,SACpBC,EAAaC,EAAQH,EAAAA,GAAOG,EAAQF,CAAAA,CAAAA;AAAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxzBrC,IAAMG,kBAAe;ACpB5B,IAAMC,IAAI,SAAJA,KAAI;AAAA,SACRC,KAAKC,OAAO,IAAID,KAAKE,OAAL,KAAiB,KAAjC,EACGC,SAAS,EADZ,EAEGC,UAAU,CAFb;AADQ;AAKH,IAAMC,OAAO,SAAPA,QAAO;AAAA,SAAA,KAASN,EAAC,IAAKA,EAAC,IAAhB,MAAsBA,EAAC,IAAvB,MAA6BA,EAAC,IAA9B,MAAoCA,EAAC,IAArC,MAA2CA,EAAC,IAAKA,EAAC,IAAKA,EAAC;AAAxD;ACHb,IAAMO,gBAAgB,SAAhBA,eAAgBC,OAAA;MAC3BC,KAAAA,MAAAA,IACAC,YAAAA,MAAAA;AAKA,MAAMC,MAAMC,aAAAA,QAAMC,OAAO,IAAb;AAEZD,eAAAA,QAAME,UAAU,WAAA;AAEd,QAAMC,WAAU;MACdC,MAAM;MACNC,YAAY;MACZC,WAAW;;AAGb,QAAMC,YAA0C,SAA1CA,WAA0CC,SAAO;AACrDX,SAAGW,QAAQC,KAAK,SAAAC,GAAC;AAAA,eAAIA,EAAEC;OAApB,CAAD;;AAGJ,QAAMC,WAAW,IAAIC,qBAAqBN,WAAWJ,QAApC;AAEjB,QAAIJ,OAAJ,QAAIA,IAAKe,SAAS;AAChBF,eAASG,QAAQhB,IAAIe,OAArB;;AAGF,WAAO,WAAA;AACLF,eAASI,WAAT;;KAED,CAACnB,EAAD,CArBH;AAuBA,SAAOG,aAAAA,QAAAA,cAAA,OAAA;IAAKD;KAAWD,SAAhB;AACR;AC7BD,IAAImB,iBAAiB;AAErB,IAAMC,KAAK,SAALA,IAAMC,IAAD;AACT,MAAI;AACFA,OAAE;WACKT,GAAG;AACV,QAAI;AACFS,SAAE;aACKT,IAAG;IAAA;;AAEf;AAED,IAAMU,gBAAe,SAAfA,aAAe,MAAA;0BACnBjB,SAAAA,UAAAA,iBAAAA,SAAU,CAAA,IAAA,yCACVkB,gBAAAA,iBAAAA,wBAAAA,SAAiB,CAAA,IAAA,6CACjBC,aAAAA,cAAAA,qBAAAA,SAAc,CAAA,IAAA,kBACdxB,WAAAA,KAAAA,yBACAyB,IAAAA,KAAAA,YAAAA,SAAK7B,KAAI,IAAA;AAET,MAAM8B,QAAQxB,aAAAA,QAAMC,OAAO;IACzBwB,mBAAmB,sBAAsBF;IACzCG,UAAU,YAAYH;IACtBI,YAAY;GAHA,EAIXb;AACH,MAAA,kBAA8Bd,aAAAA,QAAM4B,SAAS,KAAf,GAAvBC,UAAP,gBAAA,CAAA,GAAgBC,aAAhB,gBAAA,CAAA;AAEA9B,eAAAA,QAAME,UAAU,WAAA;AACd4B,eAAW,IAAD;AACV,WAAO,WAAA;AAAA,aACLZ,GAAG,WAAA;AACDa,aAAI,uBAAsBP,MAAME,WAA5B,IAAA;OADJ;;KAGH,CAAA,CANH;AAQA,MAAMM,gBACJ,OAAOC,WAAW,cACd,kBAAkBA,UAAUC,UAAUC,iBACtC;AACN,MAAMC,MAAMC,KAAKC,UAAL,SAAA;IACVC,aAAaP,gBAAgB,OAAO;IACpCQ,UAAUR,gBAAgB,OAAO;IACjCS,UAAU;IACVC,YAAY;KACTvC,OALO,CAAA;AASZ,MAAMwC,qBAAqB,SAArBA,mBAAsBC,WAAD;AAAA,WACzB1B,GAAG,WAAA;AACD,UAAI0B,aAAaf,SAAS;AACxB,YAAI,CAACZ,gBAAgB;AACnBc,eAAK5C,eAAD;AACJ8B,2BAAiB;;AAGnB,YAAIO,MAAMG,YAAY;AACpBI,eAAI,uBAAsBP,MAAME,WAA5B,IAAA;eACC;AACL,cAAI;AACFK,iBAAI,sBAAqBP,MAAME,WAA3B,cAA+CU,MAA/C,GAAA;AACJZ,kBAAMG,aAAa;mBACZjB,GAAG;AACV,gBAAMmC,KAAKC,SAASC,eAAevB,MAAMC,iBAA9B;AAEX,gBAAIoB,IAAI;AACNA,iBAAGG,MAAMC,UAAU;;AAGrB,kBAAMvC;;;aAGL;AACL,YAAIc,MAAMG,YAAY;AACpBI,eAAI,sBAAqBP,MAAME,WAA3B,IAAA;;;KAzBR;;AA8BJ,SACE1B,aAAAA,QAAAA,cAACL,eAAD;IAAeE,IAAI8C;KACjB3C,aAAAA,QAAAA,cAAA,OAAA,OAAA,OAAA;IAAKuB,IAAIC,MAAMC;KAAuBJ,cAAAA,GACpCrB,aAAAA,QAAAA,cAAA,UAAA,OAAA,OAAA;IACEuB,IAAIC,MAAME;IACVsB,OAAO;MAACE,OAAO;MAAQC,UAAU;;IACjCD,OAAO;IACPE,QAAQ;KACJ9B,WAAAA,GAEHxB,QAPH,CADF,CADF;AAcH;AAED,IAAauD,QAAQ,SAARA,OAASC,OAAD;AACnB,SAAOtD,aAAAA,QAAAA,cAACoB,eAAD,OAAA,OAAA,CAAA,GAAkBkC,OAAAA;IAAOC,KAAK7D,KAAI;IAAlC;AACR;ACvGM,IAAM8D,UAAU,SAAVA,SAAWC,OAAD;AAAA,SACrBA,MAAM,CAAD,MAAQ,MAAMA,QAAnB,MAA+BA;AADV;ACgBvB,IAAaC,mBAAmB,SAAnBA,kBAAmB9D,OAAA;0BAC9B+D,QAAAA,SAAAA,gBAAAA,SAAS,CAAA,IAAA,gCACTC,OAAAA,QAAAA,eAAAA,SAAQ,SAAA,qCACRC,aAAAA,cAAAA,qBAAAA,SAAc,SAAA,kBACdC,OAAAA,MAAAA,4BACAC,UAAAA,WAAAA,kBAAAA,SAAW,CAAA,IAAA,6CACXC,kBAAAA,mBAAAA,0BAAAA,SAAmB,IAAA,yCACnBC,MAAAA,OAAAA,cAAAA,SAAO,KAAA;AAgBP,MAAMC,cAAcV,QAAQM,KAAKK,GAAN;AAC3B,MAAMC,YAAYZ,QAAQI,KAAD;AACzB,MAAMS,kBAAkBb,QAAQK,WAAD;AAC/B,MAAMS,oBACJC,GAAaH,WAAWF,WAAZ,IAA2BF;AACzC,MAAMQ,MAAMF,oBACRG,EAAQP,WAAD,IACPO,EAAQJ,eAAD;AACX,MAAA,WAAkBG,IAAIE,IAAI,SAACC,SAAD;AAAA,WACxBtF,KAAKuF,MAAOD,UAAU,MAAO,GAA7B;GADgB,GAAXE,IAAP,SAAA,CAAA,GAAUC,IAAV,SAAA,CAAA,GAAaC,IAAb,SAAA,CAAA;AAGA,MAAMC,SAAM,sFAAuFH,IAAvF,OAA6FC,IAA7F,OAAmGC,IAAnG,qCAAuId,OAAvI,gBAAyJA,OAAzJ,iBAA4KH,KAAKmB,QAAjL,uBAA2MnB,KAAKoB,OAAhN;AAEZ,MAAMC,IAAC,SAAA;IACL5B,KAAK7D,KAAI;IACTuF,OAAOnB,KAAKmB;IACZjC,OAAO;MAACoC,QAAQ;;KACbzB,MAJE;AAOP,MAAM0B,KAAC,SAAA;IACLjC,QAAQa;IACRf,OAAOe;IACPqB,KAAKxB,KAAKmB;IACVM,KAAKP;KACFjB,QALE;AAQP,SACE/D,aAAAA,QAAAA,cAAA,KAAA,OAAA,OAAA,CAAA,GAAOmF,CAAAA,GACLnF,aAAAA,QAAAA,cAAA,OAAA,OAAA,OAAA,CAAA,GAASqF,EAAAA,CAAT,CADF;AAIH;;;;;ACjED,MAAI,UAAW,SAAU,SAAS;AAGhC,QAAI,KAAK,OAAO;AAChB,QAAI,SAAS,GAAG;AAChB,QAAIG;AACJ,QAAI,UAAU,OAAO,WAAW,aAAa,SAAS,CAAA;AACtD,QAAI,iBAAiB,QAAQ,YAAY;AACzC,QAAI,sBAAsB,QAAQ,iBAAiB;AACnD,QAAI,oBAAoB,QAAQ,eAAe;AAE/C,aAAS,OAAO,KAAK,KAAK,OAAO;AAC/B,aAAO,eAAe,KAAK,KAAK;QAC9B;QACA,YAAY;QACZ,cAAc;QACd,UAAU;MAChB,CAAK;AACD,aAAO,IAAI,GAAG;IAClB;AACE,QAAI;AAEF,aAAO,CAAA,GAAI,EAAE;IACjB,SAAW,KAAK;AACZ,eAAS,SAAS,KAAK,KAAK,OAAO;AACjC,eAAO,IAAI,GAAG,IAAI;MACxB;IACA;AAEE,aAAS,KAAK,SAAS,SAAS,MAAM,aAAa;AAEjD,UAAI,iBAAiB,WAAW,QAAQ,qBAAqB,YAAY,UAAU;AACnF,UAAI,YAAY,OAAO,OAAO,eAAe,SAAS;AACtD,UAAI,UAAU,IAAI,QAAQ,eAAe,CAAA,CAAE;AAI3C,gBAAU,UAAU,iBAAiB,SAAS,MAAM,OAAO;AAE3D,aAAO;IACX;AACE,YAAQ,OAAO;AAYf,aAAS,SAAS,IAAI,KAAK,KAAK;AAC9B,UAAI;AACF,eAAO,EAAE,MAAM,UAAU,KAAK,GAAG,KAAK,KAAK,GAAG,EAAC;MACrD,SAAa,KAAK;AACZ,eAAO,EAAE,MAAM,SAAS,KAAK,IAAG;MACtC;IACA;AAEE,QAAI,yBAAyB;AAC7B,QAAI,yBAAyB;AAC7B,QAAI,oBAAoB;AACxB,QAAI,oBAAoB;AAIxB,QAAI,mBAAmB,CAAA;AAMvB,aAAS,YAAY;IAAA;AACrB,aAAS,oBAAoB;IAAA;AAC7B,aAAS,6BAA6B;IAAA;AAItC,QAAI,oBAAoB,CAAA;AACxB,WAAO,mBAAmB,gBAAgB,WAAY;AACpD,aAAO;IACX,CAAG;AAED,QAAI,WAAW,OAAO;AACtB,QAAI,0BAA0B,YAAY,SAAS,SAAS,OAAO,CAAA,CAAE,CAAC,CAAC;AACvE,QAAI,2BACA,4BAA4B,MAC5B,OAAO,KAAK,yBAAyB,cAAc,GAAG;AAGxD,0BAAoB;IACxB;AAEE,QAAI,KAAK,2BAA2B,YAClC,UAAU,YAAY,OAAO,OAAO,iBAAiB;AACvD,sBAAkB,YAAY;AAC9B,WAAO,IAAI,eAAe,0BAA0B;AACpD,WAAO,4BAA4B,eAAe,iBAAiB;AACnE,sBAAkB,cAAc;MAC9B;MACA;MACA;IACJ;AAIE,aAAS,sBAAsB,WAAW;AACxC,OAAC,QAAQ,SAAS,QAAQ,EAAE,QAAQ,SAAS,QAAQ;AACnD,eAAO,WAAW,QAAQ,SAAS,KAAK;AACtC,iBAAO,KAAK,QAAQ,QAAQ,GAAG;QACvC,CAAO;MACP,CAAK;IACL;AAEE,YAAQ,sBAAsB,SAAS,QAAQ;AAC7C,UAAI,OAAO,OAAO,WAAW,cAAc,OAAO;AAClD,aAAO,OACH,SAAS;;OAGR,KAAK,eAAe,KAAK,UAAU,sBACpC;IACR;AAEE,YAAQ,OAAO,SAAS,QAAQ;AAC9B,UAAI,OAAO,gBAAgB;AACzB,eAAO,eAAe,QAAQ,0BAA0B;MAC9D,OAAW;AACL,eAAO,YAAY;AACnB,eAAO,QAAQ,mBAAmB,mBAAmB;MAC3D;AACI,aAAO,YAAY,OAAO,OAAO,EAAE;AACnC,aAAO;IACX;AAME,YAAQ,QAAQ,SAAS,KAAK;AAC5B,aAAO,EAAE,SAAS,IAAG;IACzB;AAEE,aAAS,cAAc,WAAW,aAAa;AAC7C,eAAS,OAAO,QAAQ,KAAK,SAAS,QAAQ;AAC5C,YAAI,SAAS,SAAS,UAAU,MAAM,GAAG,WAAW,GAAG;AACvD,YAAI,OAAO,SAAS,SAAS;AAC3B,iBAAO,OAAO,GAAG;QACzB,OAAa;AACL,cAAI,SAAS,OAAO;AACpB,cAAI,QAAQ,OAAO;AACnB,cAAI,SACA,OAAO,UAAU,YACjB,OAAO,KAAK,OAAO,SAAS,GAAG;AACjC,mBAAO,YAAY,QAAQ,MAAM,OAAO,EAAE,KAAK,SAASC,QAAO;AAC7D,qBAAO,QAAQA,QAAO,SAAS,MAAM;YACjD,GAAa,SAAS,KAAK;AACf,qBAAO,SAAS,KAAK,SAAS,MAAM;YAChD,CAAW;UACX;AAEQ,iBAAO,YAAY,QAAQ,KAAK,EAAE,KAAK,SAAS,WAAW;AAIzD,mBAAO,QAAQ;AACf,oBAAQ,MAAM;UACxB,GAAW,SAAS,OAAO;AAGjB,mBAAO,OAAO,SAAS,OAAO,SAAS,MAAM;UACvD,CAAS;QACT;MACA;AAEI,UAAI;AAEJ,eAAS,QAAQ,QAAQ,KAAK;AAC5B,iBAAS,6BAA6B;AACpC,iBAAO,IAAI,YAAY,SAAS,SAAS,QAAQ;AAC/C,mBAAO,QAAQ,KAAK,SAAS,MAAM;UAC7C,CAAS;QACT;AAEM,eAAO;;;;;;;;;;;;QAaL,kBAAkB,gBAAgB;UAChC;;;UAGA;QACV,IAAY,2BAA0B;MACtC;AAII,WAAK,UAAU;IACnB;AAEE,0BAAsB,cAAc,SAAS;AAC7C,WAAO,cAAc,WAAW,qBAAqB,WAAY;AAC/D,aAAO;IACX,CAAG;AACD,YAAQ,gBAAgB;AAKxB,YAAQ,QAAQ,SAAS,SAAS,SAAS,MAAM,aAAa,aAAa;AACzE,UAAI,gBAAgB,OAAQ,eAAc;AAE1C,UAAI,OAAO,IAAI;QACb,KAAK,SAAS,SAAS,MAAM,WAAW;QACxC;MACN;AAEI,aAAO,QAAQ,oBAAoB,OAAO,IACtC,OACA,KAAK,KAAI,EAAG,KAAK,SAAS,QAAQ;AAChC,eAAO,OAAO,OAAO,OAAO,QAAQ,KAAK,KAAI;MACvD,CAAS;IACT;AAEE,aAAS,iBAAiB,SAAS,MAAM,SAAS;AAChD,UAAIjE,SAAQ;AAEZ,aAAO,SAAS,OAAO,QAAQ,KAAK;AAClC,YAAIA,WAAU,mBAAmB;AAC/B,gBAAM,IAAI,MAAM,8BAA8B;QACtD;AAEM,YAAIA,WAAU,mBAAmB;AAC/B,cAAI,WAAW,SAAS;AACtB,kBAAM;UAChB;AAIQ,iBAAO,WAAU;QACzB;AAEM,gBAAQ,SAAS;AACjB,gBAAQ,MAAM;AAEd,eAAO,MAAM;AACX,cAAI,WAAW,QAAQ;AACvB,cAAI,UAAU;AACZ,gBAAI,iBAAiB,oBAAoB,UAAU,OAAO;AAC1D,gBAAI,gBAAgB;AAClB,kBAAI,mBAAmB,iBAAkB;AACzC,qBAAO;YACnB;UACA;AAEQ,cAAI,QAAQ,WAAW,QAAQ;AAG7B,oBAAQ,OAAO,QAAQ,QAAQ,QAAQ;UAEjD,WAAmB,QAAQ,WAAW,SAAS;AACrC,gBAAIA,WAAU,wBAAwB;AACpC,cAAAA,SAAQ;AACR,oBAAM,QAAQ;YAC1B;AAEU,oBAAQ,kBAAkB,QAAQ,GAAG;UAE/C,WAAmB,QAAQ,WAAW,UAAU;AACtC,oBAAQ,OAAO,UAAU,QAAQ,GAAG;UAC9C;AAEQ,UAAAA,SAAQ;AAER,cAAI,SAAS,SAAS,SAAS,MAAM,OAAO;AAC5C,cAAI,OAAO,SAAS,UAAU;AAG5B,YAAAA,SAAQ,QAAQ,OACZ,oBACA;AAEJ,gBAAI,OAAO,QAAQ,kBAAkB;AACnC;YACZ;AAEU,mBAAO;cACL,OAAO,OAAO;cACd,MAAM,QAAQ;YAC1B;UAEA,WAAmB,OAAO,SAAS,SAAS;AAClC,YAAAA,SAAQ;AAGR,oBAAQ,SAAS;AACjB,oBAAQ,MAAM,OAAO;UAC/B;QACA;MACA;IACA;AAME,aAAS,oBAAoB,UAAU,SAAS;AAC9C,UAAI,SAAS,SAAS,SAAS,QAAQ,MAAM;AAC7C,UAAI,WAAWgE,aAAW;AAGxB,gBAAQ,WAAW;AAEnB,YAAI,QAAQ,WAAW,SAAS;AAE9B,cAAI,SAAS,SAAS,QAAQ,GAAG;AAG/B,oBAAQ,SAAS;AACjB,oBAAQ,MAAMA;AACd,gCAAoB,UAAU,OAAO;AAErC,gBAAI,QAAQ,WAAW,SAAS;AAG9B,qBAAO;YACnB;UACA;AAEQ,kBAAQ,SAAS;AACjB,kBAAQ,MAAM,IAAI;YAChB;UAAgD;QAC1D;AAEM,eAAO;MACb;AAEI,UAAI,SAAS,SAAS,QAAQ,SAAS,UAAU,QAAQ,GAAG;AAE5D,UAAI,OAAO,SAAS,SAAS;AAC3B,gBAAQ,SAAS;AACjB,gBAAQ,MAAM,OAAO;AACrB,gBAAQ,WAAW;AACnB,eAAO;MACb;AAEI,UAAI,OAAO,OAAO;AAElB,UAAI,CAAE,MAAM;AACV,gBAAQ,SAAS;AACjB,gBAAQ,MAAM,IAAI,UAAU,kCAAkC;AAC9D,gBAAQ,WAAW;AACnB,eAAO;MACb;AAEI,UAAI,KAAK,MAAM;AAGb,gBAAQ,SAAS,UAAU,IAAI,KAAK;AAGpC,gBAAQ,OAAO,SAAS;AAQxB,YAAI,QAAQ,WAAW,UAAU;AAC/B,kBAAQ,SAAS;AACjB,kBAAQ,MAAMA;QACtB;MAEA,OAAW;AAEL,eAAO;MACb;AAII,cAAQ,WAAW;AACnB,aAAO;IACX;AAIE,0BAAsB,EAAE;AAExB,WAAO,IAAI,mBAAmB,WAAW;AAOzC,WAAO,IAAI,gBAAgB,WAAW;AACpC,aAAO;IACX,CAAG;AAED,WAAO,IAAI,YAAY,WAAW;AAChC,aAAO;IACX,CAAG;AAED,aAAS,aAAa,MAAM;AAC1B,UAAI,QAAQ,EAAE,QAAQ,KAAK,CAAC,EAAC;AAE7B,UAAI,KAAK,MAAM;AACb,cAAM,WAAW,KAAK,CAAC;MAC7B;AAEI,UAAI,KAAK,MAAM;AACb,cAAM,aAAa,KAAK,CAAC;AACzB,cAAM,WAAW,KAAK,CAAC;MAC7B;AAEI,WAAK,WAAW,KAAK,KAAK;IAC9B;AAEE,aAAS,cAAc,OAAO;AAC5B,UAAI,SAAS,MAAM,cAAc,CAAA;AACjC,aAAO,OAAO;AACd,aAAO,OAAO;AACd,YAAM,aAAa;IACvB;AAEE,aAAS,QAAQ,aAAa;AAI5B,WAAK,aAAa,CAAC,EAAE,QAAQ,OAAM,CAAE;AACrC,kBAAY,QAAQ,cAAc,IAAI;AACtC,WAAK,MAAM,IAAI;IACnB;AAEE,YAAQ,OAAO,SAAS,QAAQ;AAC9B,UAAI,OAAO,CAAA;AACX,eAAS,OAAO,QAAQ;AACtB,aAAK,KAAK,GAAG;MACnB;AACI,WAAK,QAAO;AAIZ,aAAO,SAAS,OAAO;AACrB,eAAO,KAAK,QAAQ;AAClB,cAAIjC,OAAM,KAAK,IAAG;AAClB,cAAIA,QAAO,QAAQ;AACjB,iBAAK,QAAQA;AACb,iBAAK,OAAO;AACZ,mBAAO;UACjB;QACA;AAKM,aAAK,OAAO;AACZ,eAAO;MACb;IACA;AAEE,aAAS,OAAO,UAAU;AACxB,UAAI,UAAU;AACZ,YAAI,iBAAiB,SAAS,cAAc;AAC5C,YAAI,gBAAgB;AAClB,iBAAO,eAAe,KAAK,QAAQ;QAC3C;AAEM,YAAI,OAAO,SAAS,SAAS,YAAY;AACvC,iBAAO;QACf;AAEM,YAAI,CAAC,MAAM,SAAS,MAAM,GAAG;AAC3B,cAAI8B,KAAI,IAAI,OAAO,SAASK,QAAO;AACjC,mBAAO,EAAEL,KAAI,SAAS,QAAQ;AAC5B,kBAAI,OAAO,KAAK,UAAUA,EAAC,GAAG;AAC5B,gBAAAK,MAAK,QAAQ,SAASL,EAAC;AACvB,gBAAAK,MAAK,OAAO;AACZ,uBAAOA;cACrB;YACA;AAEU,YAAAA,MAAK,QAAQF;AACb,YAAAE,MAAK,OAAO;AAEZ,mBAAOA;UACjB;AAEQ,iBAAO,KAAK,OAAO;QAC3B;MACA;AAGI,aAAO,EAAE,MAAM,WAAU;IAC7B;AACE,YAAQ,SAAS;AAEjB,aAAS,aAAa;AACpB,aAAO,EAAE,OAAOF,aAAW,MAAM,KAAI;IACzC;AAEE,YAAQ,YAAY;MAClB,aAAa;MAEb,OAAO,SAAS,eAAe;AAC7B,aAAK,OAAO;AACZ,aAAK,OAAO;AAGZ,aAAK,OAAO,KAAK,QAAQA;AACzB,aAAK,OAAO;AACZ,aAAK,WAAW;AAEhB,aAAK,SAAS;AACd,aAAK,MAAMA;AAEX,aAAK,WAAW,QAAQ,aAAa;AAErC,YAAI,CAAC,eAAe;AAClB,mBAAS,QAAQ,MAAM;AAErB,gBAAI,KAAK,OAAO,CAAC,MAAM,OACnB,OAAO,KAAK,MAAM,IAAI,KACtB,CAAC,MAAM,CAAC,KAAK,MAAM,CAAC,CAAC,GAAG;AAC1B,mBAAK,IAAI,IAAIA;YACzB;UACA;QACA;MACA;MAEI,MAAM,WAAW;AACf,aAAK,OAAO;AAEZ,YAAI,YAAY,KAAK,WAAW,CAAC;AACjC,YAAI,aAAa,UAAU;AAC3B,YAAI,WAAW,SAAS,SAAS;AAC/B,gBAAM,WAAW;QACzB;AAEM,eAAO,KAAK;MAClB;MAEI,mBAAmB,SAAS,WAAW;AACrC,YAAI,KAAK,MAAM;AACb,gBAAM;QACd;AAEM,YAAI,UAAU;AACd,iBAAS,OAAO,KAAK,QAAQ;AAC3B,iBAAO,OAAO;AACd,iBAAO,MAAM;AACb,kBAAQ,OAAO;AAEf,cAAI,QAAQ;AAGV,oBAAQ,SAAS;AACjB,oBAAQ,MAAMA;UACxB;AAEQ,iBAAO,CAAC,CAAE;QAClB;AAEM,iBAASH,KAAI,KAAK,WAAW,SAAS,GAAGA,MAAK,GAAG,EAAEA,IAAG;AACpD,cAAI,QAAQ,KAAK,WAAWA,EAAC;AAC7B,cAAI,SAAS,MAAM;AAEnB,cAAI,MAAM,WAAW,QAAQ;AAI3B,mBAAO,OAAO,KAAK;UAC7B;AAEQ,cAAI,MAAM,UAAU,KAAK,MAAM;AAC7B,gBAAI,WAAW,OAAO,KAAK,OAAO,UAAU;AAC5C,gBAAI,aAAa,OAAO,KAAK,OAAO,YAAY;AAEhD,gBAAI,YAAY,YAAY;AAC1B,kBAAI,KAAK,OAAO,MAAM,UAAU;AAC9B,uBAAO,OAAO,MAAM,UAAU,IAAI;cAChD,WAAuB,KAAK,OAAO,MAAM,YAAY;AACvC,uBAAO,OAAO,MAAM,UAAU;cAC5C;YAEA,WAAqB,UAAU;AACnB,kBAAI,KAAK,OAAO,MAAM,UAAU;AAC9B,uBAAO,OAAO,MAAM,UAAU,IAAI;cAChD;YAEA,WAAqB,YAAY;AACrB,kBAAI,KAAK,OAAO,MAAM,YAAY;AAChC,uBAAO,OAAO,MAAM,UAAU;cAC5C;YAEA,OAAiB;AACL,oBAAM,IAAI,MAAM,wCAAwC;YACpE;UACA;QACA;MACA;MAEI,QAAQ,SAAS,MAAM,KAAK;AAC1B,iBAASA,KAAI,KAAK,WAAW,SAAS,GAAGA,MAAK,GAAG,EAAEA,IAAG;AACpD,cAAI,QAAQ,KAAK,WAAWA,EAAC;AAC7B,cAAI,MAAM,UAAU,KAAK,QACrB,OAAO,KAAK,OAAO,YAAY,KAC/B,KAAK,OAAO,MAAM,YAAY;AAChC,gBAAI,eAAe;AACnB;UACV;QACA;AAEM,YAAI,iBACC,SAAS,WACT,SAAS,eACV,aAAa,UAAU,OACvB,OAAO,aAAa,YAAY;AAGlC,yBAAe;QACvB;AAEM,YAAI,SAAS,eAAe,aAAa,aAAa,CAAA;AACtD,eAAO,OAAO;AACd,eAAO,MAAM;AAEb,YAAI,cAAc;AAChB,eAAK,SAAS;AACd,eAAK,OAAO,aAAa;AACzB,iBAAO;QACf;AAEM,eAAO,KAAK,SAAS,MAAM;MACjC;MAEI,UAAU,SAAS,QAAQ,UAAU;AACnC,YAAI,OAAO,SAAS,SAAS;AAC3B,gBAAM,OAAO;QACrB;AAEM,YAAI,OAAO,SAAS,WAChB,OAAO,SAAS,YAAY;AAC9B,eAAK,OAAO,OAAO;QAC3B,WAAiB,OAAO,SAAS,UAAU;AACnC,eAAK,OAAO,KAAK,MAAM,OAAO;AAC9B,eAAK,SAAS;AACd,eAAK,OAAO;QACpB,WAAiB,OAAO,SAAS,YAAY,UAAU;AAC/C,eAAK,OAAO;QACpB;AAEM,eAAO;MACb;MAEI,QAAQ,SAAS,YAAY;AAC3B,iBAASA,KAAI,KAAK,WAAW,SAAS,GAAGA,MAAK,GAAG,EAAEA,IAAG;AACpD,cAAI,QAAQ,KAAK,WAAWA,EAAC;AAC7B,cAAI,MAAM,eAAe,YAAY;AACnC,iBAAK,SAAS,MAAM,YAAY,MAAM,QAAQ;AAC9C,0BAAc,KAAK;AACnB,mBAAO;UACjB;QACA;MACA;MAEI,SAAS,SAAS,QAAQ;AACxB,iBAASA,KAAI,KAAK,WAAW,SAAS,GAAGA,MAAK,GAAG,EAAEA,IAAG;AACpD,cAAI,QAAQ,KAAK,WAAWA,EAAC;AAC7B,cAAI,MAAM,WAAW,QAAQ;AAC3B,gBAAI,SAAS,MAAM;AACnB,gBAAI,OAAO,SAAS,SAAS;AAC3B,kBAAI,SAAS,OAAO;AACpB,4BAAc,KAAK;YAC/B;AACU,mBAAO;UACjB;QACA;AAIM,cAAM,IAAI,MAAM,uBAAuB;MAC7C;MAEI,eAAe,SAAS,UAAU,YAAY,SAAS;AACrD,aAAK,WAAW;UACd,UAAU,OAAO,QAAQ;UACzB;UACA;QACR;AAEM,YAAI,KAAK,WAAW,QAAQ;AAG1B,eAAK,MAAMG;QACnB;AAEM,eAAO;MACb;IACA;AAME,WAAO;EAET;;;;;IAK+B,OAAO;EACtC;AAEA,MAAI;AACF,yBAAqB;EACvB,SAAS,sBAAsB;AAW7B,QAAI,OAAO,eAAe,UAAU;AAClC,iBAAW,qBAAqB;IACpC,OAAS;AACL,eAAS,KAAK,wBAAwB,EAAE,OAAO;IACnD;EACA;;ACjvBO,IAAMG,YAAY,SAAZA,WAAaC,KAAD;AACvB,MAAMC,KAAKD,IAAIE,QAAQ,KAAZ;AACX,MAAMC,KAAKH,IAAIE,QAAQ,KAAKD,KAAK,CAAtB;AACX,SAAOD,IAAInG,UAAUoG,KAAK,GAAGE,EAAtB;AACR;ACFD,IAAMC,MAAM;AACZ,IAAMC,QAAgC,CAAA;AACtC,IAAMC,WAAW;AAEjB,IAAMC,cAAW,WAAA;AAAA,MAAAvG,QAAA,kBAAA,UAAA,KAAG,SAAA,QAAOwG,MAAP;AAAA,QAAA,SAAA,KAAA,MAAA;AAAA,WAAA,UAAA,KAAA,SAAA,SAAA,UAAA;AAAA,aAAA,GAAA;AAAA,gBAAA,SAAA,OAAA,SAAA,MAAA;UAAA,KAAA;AAAA,gBACbH,MAAMG,IAAD,GADQ;AAAA,uBAAA,OAAA;AAAA;;AAAA,qBAAA,OAAA;AAGRC,sBAHQ,KAGKL,MAAMI,OAHX;AAAA,qBAAA,OAAA;AAAA,mBAIIE,MAAMD,SAAS;cAACJ,OAAO;aAAlB;UAJT,KAAA;AAIRM,kBAJQ,SAAA;AAAA,qBAAA,OAAA;AAAA,mBAKKA,IAAIC,KAAJ;UALL,KAAA;AAKRA,mBALQ,SAAA;AAMRtB,mBAAOS,UAAUa,IAAD;AACtBP,kBAAMG,IAAD,IAASlB;AAPA,qBAAA,OAAA;AAAA;UAAA,KAAA;AAAA,qBAAA,OAAA;AAAA,qBAAA,KAAA,SAAA,OAAA,EAAA,CAAA;UAAA,KAAA;AAAA,gBAAA,CAWde,MAAMG,IAAD,GAXS;AAAA,uBAAA,OAAA;AAAA;;AAAA,mBAAA,SAAA,OAAA,UAYTH,MAAMG,IAAD,CAZI;UAAA,KAAA;AAAA,mBAAA,SAAA,OAAA,UAeXF,QAfW;UAAA,KAAA;UAAA,KAAA;AAAA,mBAAA,SAAA,KAAA;;;;GAAH,CAAA;AAAA,SAAA,SAAXC,aAAW,IAAA;AAAA,WAAAvG,MAAA,MAAA,MAAA,SAAA;;AAAA,EAAA;AAkBV,IAAM6G,eAAY,WAAA;AAAA,MAAA,QAAA,kBAAA,UAAA,KAAG,SAAA,SAAOC,OAAP;AAAA,QAAA;AAAA,WAAA,UAAA,KAAA,SAAA,UAAA,WAAA;AAAA,aAAA,GAAA;AAAA,gBAAA,UAAA,OAAA,UAAA,MAAA;UAAA,KAAA;AACpBhC,kBAAM,CAAA;AACZgC,kBAAMC,QAAQ,SAAAvH,IAAC;AACbsF,kBAAItF,EAAD,IAAMA;aADX;AAF0B,sBAAA,OAAA;AAAA,mBAKpBwH,QAAQC,IAAIC,OAAOC,KAAKrC,GAAZ,EAAiBA,IAAIyB,WAArB,CAAZ;UALoB,KAAA;AAAA,mBAAA,UAAA,OAAA,UAMnBO,MAAMhC,IAAI,SAAA0B,MAAI;AAAA,kBAAA;AAAA,qBAAK;gBACxBA;gBACAlB,OAAI,cAAEe,MAAMG,IAAD,MAAP,OAAA,cAAiBF;;aAFhB,CANmB;UAAA,KAAA;UAAA,KAAA;AAAA,mBAAA,UAAA,KAAA;;;;GAAH,CAAA;AAAA,SAAA,SAAZO,cAAY,KAAA;AAAA,WAAA,MAAA,MAAA,MAAA,SAAA;;AAAA,EAAA;ACvBzB,IAAMO,6BAAqD;EACzD,KAAK;EACL,KAAK;EACL,KAAK;EACLC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;AAXsD;AAc3D,IAAMC,4BAA4BC,OAAM,MAClCZ,OAAOC,KAAKC,0BAAZ,EAAwCW,KAAK,EAA7C,IADkC,KAEtC,GAFsC;AAKxC,IAAMC,4BAA4B;AAElC,IAAaC,UAAU,SAAVA,SAAUjI,OAAA;AAAA,MAAEqF,QAAFrF,MAAEqF;AAAF,SACrBA,MACG6C,YADH,EAEGC,QACCN,2BACA,SAAAO,OAAI;AAAA,WAAIhB,2BAA2BgB,KAAD;GAJtC,EAMGC,UAAU,KANb,EAOGF,QAAQH,2BAA2B,EAPtC;AADqB;ACnBvB,IAAM5B,QACJ;AACF,IAAIC;AAGJ,IAAMC,aAAW;AAQjB,IAAMgC,aAAU,WAAA;AAAA,MAAAtI,QAAA,kBAAA,UAAA,KAAG,SAAA,UAAA;AAAA,QAAA,KAAA;AAAA,WAAA,UAAA,KAAA,SAAA,SAAA,UAAA;AAAA,aAAA,GAAA;AAAA,gBAAA,SAAA,OAAA,SAAA,MAAA;UAAA,KAAA;AAAA,gBACZqG,SADY;AAAA,uBAAA,OAAA;AAAA;;AAAA,qBAAA,OAAA;AAAA,qBAAA,OAAA;AAAA,mBAGKK,MAAMN,OAAK;cAACC,OAAO;aAAd;UAHV,KAAA;AAGPM,kBAHO,SAAA;AAAA,qBAAA,OAAA;AAAA,mBAIMA,IAAI4B,KAAJ;UAJN,KAAA;AAIPA,mBAJO,SAAA;AAKblC,sBAAQ,CAAA;AACRkC,iBAAKC,MAAMzB,QAAQ,SAAC7C,MAAD;;AACjB,kBAAMuE,WAAWR,QAAQ;gBAAC5C,OAAOnB,KAAKmB;eAAd;AACxBgB,sBAAOoC,QAAD,IAAa;gBACjBlE,KAAKX,SAAO,YAACM,KAAKK,QAAN,OAAA,YAAa+B,UAAb;gBACZjB,OAAOnB,KAAKmB;gBACZmB,MAAMiC;;aALV;AANa,qBAAA,OAAA;AAAA;UAAA,KAAA;AAAA,qBAAA,OAAA;AAAA,qBAAA,KAAA,SAAA,OAAA,EAAA,CAAA;UAAA,KAAA;UAAA,KAAA;AAAA,mBAAA,SAAA,KAAA;;;;GAAH,CAAA;AAAA,SAAA,SAAVH,cAAU;AAAA,WAAAtI,MAAA,MAAA,MAAA,SAAA;;AAAA,EAAA;AAkBT,IAAM0I,cAAW,WAAA;AAAA,MAAA,QAAA,kBAAA,UAAA,KAAG,SAAA,SAAO5B,OAAP;AAAA,WAAA,UAAA,KAAA,SAAA,UAAA,WAAA;AAAA,aAAA,GAAA;AAAA,gBAAA,UAAA,OAAA,UAAA,MAAA;UAAA,KAAA;AAAA,sBAAA,OAAA;AAAA,mBACnBwB,WAAU;UADS,KAAA;AAAA,mBAAA,UAAA,OAAA,UAElB;cACLK,MAAM7B,MAAMhC,IAAI,SAAA0B,MAAI;AAAA,oBAAA,iBAAA,aAAA,mBAAA;AAAA,uBAAK;kBACvBA;kBACAjC,KAAK8B,WAAK,mBAAA,cAAGA,QAAMG,IAAD,MAAR,OAAA,SAAG,YAAajC,QAAhB,OAAA,kBAAuB+B,aAAWA;kBAC5CjB,OAAOgB,WAAK,qBAAA,eAAGA,QAAMG,IAAD,MAAR,OAAA,SAAG,aAAanB,UAAhB,OAAA,oBAAyB,SAAS;;eAH1C;cAKNgB,OAAAA;aARuB;UAAA,KAAA;UAAA,KAAA;AAAA,mBAAA,UAAA,KAAA;;;;GAAH,CAAA;AAAA,SAAA,SAAXqC,aAAW,IAAA;AAAA,WAAA,MAAA,MAAA,MAAA,SAAA;;AAAA,EAAA;IC9BXE,mBAAgB,WAAA;AAAA,MAAA,QAAA,kBAAA,UAAA,KAAG,SAAA,QAAA5I,OAAA;AAAA,QAAA,OAAA,oBAAA,OAAA,qBAAA,MAAAqG,QAAA;AAAA,WAAA,UAAA,KAAA,SAAA,SAAA,UAAA;AAAA,aAAA,GAAA;AAAA,gBAAA,SAAA,OAAA,SAAA,MAAA;UAAA,KAAA;AAAQS,oBAAR9G,MAAQ8G;AAAR,qBAAA,OAAA;AAAA,mBACOE,QAAQC,IAAI,CAC/CJ,aAAaC,KAAD,GACZ4B,YAAY5B,KAAD,CAFoC,CAAZ;UADP,KAAA;AAAA,iCAAA,SAAA;AACvB+B,oBADuB,mBAAA,CAAA;AAAA,kCAAA,mBAAA,CAAA;AACfF,mBADe,oBACfA;AAAMtC,YAAAA,SADS,oBACTA;AAIfvB,kBAAM,CAAA;AACZ6D,iBAAK5B,QAAQ,SAAAxC,KAAG;AACdO,kBAAIP,IAAIiC,IAAL,IAAajC;aADlB;AAGAsE,kBAAM9B,QAAQ,SAAAzB,MAAI;AAChBR,kBAAIQ,KAAKkB,IAAN,EAAYlB,OAAOA,KAAKA;aAD7B;AAGAwB,kBAAMC,QAAQ,SAAAvH,IAAC;AACb,kBAAMsJ,IAAIhE,IAAItF,EAAD;AACb,kBAAI,CAACsJ,EAAEvE,OAAO,CAACuE,EAAExD,MAAM;AACrB,oBAAIyD,MAAuC;AACzCC,0BAAQC,MAAR,0DAC0DH,EAAEtC,OAD5D,wCAAA;;AAIF,uBAAO1B,IAAItF,EAAD;;aARd;AAZ8B,mBAAA,SAAA,OAAA,UAuBvB;cACL0J,aAAapE;cACbqE,SAAS9C;aAzBmB;UAAA,KAAA;UAAA,KAAA;AAAA,mBAAA,SAAA,KAAA;;;;GAAH,CAAA;AAAA,SAAA,SAAhBuC,kBAAgB,IAAA;AAAA,WAAA,MAAA,MAAA,MAAA,SAAA;;AAAA,EAAA;", "names": ["precision", "max", "Math", "min", "pow", "Math", "epsilon", "pow", "kappa", "rgb2contrast", "rgb1", "rgb2", "relativeLuminance1", "relativeLuminance2", "l1", "max", "l2", "min", "precision", "rgb2luminance", "rgbR", "rgbG", "rgbB", "adjustChannel", "coefficientR", "coefficientG", "coefficientB", "x", "lowc", "<PERSON><PERSON><PERSON><PERSON>", "pow", "hex2rgb", "hex", "match", "hexColorMatch", "r", "g", "b", "a", "rr", "gg", "bb", "aa", "parseInt", "map", "c", "hexColorMatch", "hex2contrast", "hex1", "hex2", "rgb2contrast", "hex2rgb", "tagCanvasString", "s", "Math", "floor", "random", "toString", "substring", "guid", "UseInViewport", "_ref", "cb", "children", "ref", "React", "useRef", "useEffect", "options", "root", "rootMargin", "threshold", "wrappedCb", "entries", "some", "e", "isIntersecting", "observer", "IntersectionObserver", "current", "observe", "disconnect", "isScriptLoaded", "tr", "fn", "CloudWrapped", "containerProps", "canvasProps", "id", "state", "canvasContainerId", "canvasId", "hasStarted", "useState", "mounted", "setMounted", "eval", "supportsTouch", "window", "navigator", "maxTouchPoints", "ops", "JSON", "stringify", "dragControl", "maxSpeed", "textFont", "textColour", "onVisibilityChange", "isVisible", "el", "document", "getElementById", "style", "display", "width", "max<PERSON><PERSON><PERSON>", "height", "Cloud", "props", "key", "addHash", "color", "renderSimpleIcon", "aProps", "bgHex", "fallbackHex", "icon", "imgProps", "minContrastRatio", "size", "originalHex", "hex", "bgHexHash", "fallbackHexHash", "isAccessibleColor", "hex2contrast", "rgb", "hex2rgb", "map", "percent", "round", "r", "g", "b", "imgSrc", "title", "path", "a", "cursor", "i", "alt", "src", "undefined", "value", "next", "svgToPath", "svg", "p0", "indexOf", "p1", "url", "cache", "fallback", "get<PERSON>lug<PERSON><PERSON>", "slug", "urlSlug", "fetch", "res", "text", "getSlugs<PERSON>ath", "slugs", "for<PERSON>ach", "Promise", "all", "Object", "keys", "TITLE_TO_SLUG_REPLACEMENTS", "đ", "ħ", "ı", "ĸ", "ŀ", "ł", "ß", "ŧ", "TITLE_TO_SLUG_CHARS_REGEX", "RegExp", "join", "TITLE_TO_SLUG_RANGE_REGEX", "getSlug", "toLowerCase", "replace", "char", "normalize", "primeCache", "json", "icons", "iconSlug", "getSlugHexs", "hexs", "fetchSimpleIcons", "paths", "o", "process", "console", "error", "simpleIcons", "allIcon"]}