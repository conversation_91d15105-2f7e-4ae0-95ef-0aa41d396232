{"version": 3, "sources": ["../../lenis/package.json", "../../lenis/packages/core/src/maths.ts", "../../lenis/packages/core/src/animate.ts", "../../lenis/packages/core/src/debounce.ts", "../../lenis/packages/core/src/dimensions.ts", "../../lenis/packages/core/src/emitter.ts", "../../lenis/packages/core/src/virtual-scroll.ts", "../../lenis/packages/core/src/lenis.ts", "../../lenis/packages/react/src/provider.tsx", "../../lenis/packages/react/src/store.ts", "../../lenis/packages/react/src/use-lenis.ts"], "sourcesContent": ["{\n  \"name\": \"lenis\",\n  \"version\": \"1.2.3\",\n  \"description\": \"How smooth scroll should be\",\n  \"type\": \"module\",\n  \"sideEffects\": false,\n  \"author\": \"darkroom.engineering\",\n  \"license\": \"MIT\",\n  \"repository\": {\n    \"type\": \"git\",\n    \"url\": \"git+https://github.com/darkroomengineering/lenis.git\"\n  },\n  \"bugs\": {\n    \"url\": \"https://github.com/darkroomengineering/lenis/issues\"\n  },\n  \"homepage\": \"https://github.com/darkroomengineering/lenis\",\n  \"funding\": {\n    \"type\": \"github\",\n    \"url\": \"https://github.com/sponsors/darkroomengineering\"\n  },\n  \"keywords\": [\n    \"scroll\",\n    \"smooth\",\n    \"lenis\",\n    \"react\",\n    \"vue\"\n  ],\n  \"scripts\": {\n    \"build\": \"pnpm build:core && pnpm build:all\",\n    \"build:core\": \"tsup --config tsup.core.ts\",\n    \"build:all\": \"tsup\",\n    \"dev\": \"pnpm run -w --parallel /^dev:.*/\",\n    \"dev:build\": \"tsup --watch\",\n    \"dev:playground\": \"pnpm --filter playground dev\",\n    \"dev:nuxt\": \"pnpm --filter playground-nuxt dev\",\n    \"readme\": \"node ./scripts/update-readme.js\",\n    \"version:dev\": \"npm version prerelease --preid dev --force --no-git-tag-version\",\n    \"version:patch\": \"npm version patch --force --no-git-tag-version\",\n    \"version:minor\": \"npm version minor --force --no-git-tag-version\",\n    \"version:major\": \"npm version major --force --no-git-tag-version\",\n    \"postversion\": \"pnpm build && pnpm readme\",\n    \"publish:main\": \"npm publish\",\n    \"publish:dev\": \"npm publish --tag dev\"\n  },\n  \"files\": [\n    \"dist\"\n  ],\n  \"devDependencies\": {\n    \"terser\": \"^5.37.0\",\n    \"tsup\": \"^8.3.5\",\n    \"typescript\": \"^5.7.3\"\n  },\n  \"peerDependencies\": {\n    \"react\": \">=17.0.0\",\n    \"vue\": \">=3.0.0\",\n    \"@nuxt/kit\": \">=3.0.0\"\n  },\n  \"peerDependenciesMeta\": {\n    \"react\": {\n      \"optional\": true\n    },\n    \"vue\": {\n      \"optional\": true\n    },\n    \"@nuxt/kit\": {\n      \"optional\": true\n    }\n  },\n  \"unpkg\": \"./dist/lenis.mjs\",\n  \"main\": \"./dist/lenis.mjs\",\n  \"module\": \"./dist/lenis.mjs\",\n  \"types\": \"./dist/lenis.d.ts\",\n  \"exports\": {\n    \".\": {\n      \"types\": \"./dist/lenis.d.ts\",\n      \"default\": \"./dist/lenis.mjs\"\n    },\n    \"./react\": {\n      \"types\": \"./dist/lenis-react.d.ts\",\n      \"default\": \"./dist/lenis-react.mjs\"\n    },\n    \"./snap\": {\n      \"types\": \"./dist/lenis-snap.d.ts\",\n      \"default\": \"./dist/lenis-snap.mjs\"\n    },\n    \"./vue\": {\n      \"types\": \"./dist/lenis-vue.d.ts\",\n      \"default\": \"./dist/lenis-vue.mjs\"\n    },\n    \"./nuxt\": {\n      \"default\": \"./dist/lenis-vue-nuxt.mjs\"\n    },\n    \"./nuxt/runtime/*\": {\n      \"default\": \"./dist/nuxt/runtime/*.mjs\"\n    },\n    \"./dist/*\": \"./dist/*\"\n  }\n}\n", "/**\r\n * Clamp a value between a minimum and maximum value\r\n *\r\n * @param min Minimum value\r\n * @param input Value to clamp\r\n * @param max Maximum value\r\n * @returns Clamped value\r\n */\r\nexport function clamp(min: number, input: number, max: number) {\r\n  return Math.max(min, Math.min(input, max))\r\n}\r\n\r\n/**\r\n * Truncate a floating-point number to a specified number of decimal places\r\n *\r\n * @param value Value to truncate\r\n * @param decimals Number of decimal places to truncate to\r\n * @returns Truncated value\r\n */\r\nexport function truncate(value: number, decimals = 0) {\r\n  return parseFloat(value.toFixed(decimals))\r\n}\r\n\r\n/**\r\n *  Linearly interpolate between two values using an amount (0 <= t <= 1)\r\n *\r\n * @param x First value\r\n * @param y Second value\r\n * @param t Amount to interpolate (0 <= t <= 1)\r\n * @returns Interpolated value\r\n */\r\nexport function lerp(x: number, y: number, t: number) {\r\n  return (1 - t) * x + t * y\r\n}\r\n\r\n/**\r\n * Damp a value over time using a damping factor\r\n * {@link http://www.rorydriscoll.com/2016/03/07/frame-rate-independent-damping-using-lerp/}\r\n *\r\n * @param x Initial value\r\n * @param y Target value\r\n * @param lambda Damping factor\r\n * @param dt Time elapsed since the last update\r\n * @returns Damped value\r\n */\r\nexport function damp(x: number, y: number, lambda: number, deltaTime: number) {\r\n  return lerp(x, y, 1 - Math.exp(-lambda * deltaTime))\r\n}\r\n\r\n/**\r\n * Calculate the modulo of the dividend and divisor while keeping the result within the same sign as the divisor\r\n * {@link https://anguscroll.com/just/just-modulo}\r\n *\r\n * @param n Dividend\r\n * @param d Divisor\r\n * @returns Modulo\r\n */\r\nexport function modulo(n: number, d: number) {\r\n  return ((n % d) + d) % d\r\n}\r\n", "import { clamp, damp } from './maths'\r\nimport type { EasingFunction, FromToOptions, OnUpdateCallback } from './types'\r\n\r\n/**\r\n * Animate class to handle value animations with lerping or easing\r\n *\r\n * @example\r\n * const animate = new Animate()\r\n * animate.fromTo(0, 100, { duration: 1, easing: (t) => t })\r\n * animate.advance(0.5) // 50\r\n */\r\nexport class Animate {\r\n  isRunning = false\r\n  value = 0\r\n  from = 0\r\n  to = 0\r\n  currentTime = 0\r\n\r\n  // These are instanciated in the fromTo method\r\n  lerp?: number\r\n  duration?: number\r\n  easing?: EasingFunction\r\n  onUpdate?: OnUpdateCallback\r\n\r\n  /**\r\n   * Advance the animation by the given delta time\r\n   *\r\n   * @param deltaTime - The time in seconds to advance the animation\r\n   */\r\n  advance(deltaTime: number) {\r\n    if (!this.isRunning) return\r\n\r\n    let completed = false\r\n\r\n    if (this.duration && this.easing) {\r\n      this.currentTime += deltaTime\r\n      const linearProgress = clamp(0, this.currentTime / this.duration, 1)\r\n\r\n      completed = linearProgress >= 1\r\n      const easedProgress = completed ? 1 : this.easing(linearProgress)\r\n      this.value = this.from + (this.to - this.from) * easedProgress\r\n    } else if (this.lerp) {\r\n      this.value = damp(this.value, this.to, this.lerp * 60, deltaTime)\r\n      if (Math.round(this.value) === this.to) {\r\n        this.value = this.to\r\n        completed = true\r\n      }\r\n    } else {\r\n      // If no easing or lerp, just jump to the end value\r\n      this.value = this.to\r\n      completed = true\r\n    }\r\n\r\n    if (completed) {\r\n      this.stop()\r\n    }\r\n\r\n    // Call the onUpdate callback with the current value and completed status\r\n    this.onUpdate?.(this.value, completed)\r\n  }\r\n\r\n  /** Stop the animation */\r\n  stop() {\r\n    this.isRunning = false\r\n  }\r\n\r\n  /**\r\n   * Set up the animation from a starting value to an ending value\r\n   * with optional parameters for lerping, duration, easing, and onUpdate callback\r\n   *\r\n   * @param from - The starting value\r\n   * @param to - The ending value\r\n   * @param options - Options for the animation\r\n   */\r\n  fromTo(\r\n    from: number,\r\n    to: number,\r\n    { lerp, duration, easing, onStart, onUpdate }: FromToOptions\r\n  ) {\r\n    this.from = this.value = from\r\n    this.to = to\r\n    this.lerp = lerp\r\n    this.duration = duration\r\n    this.easing = easing\r\n    this.currentTime = 0\r\n    this.isRunning = true\r\n\r\n    onStart?.()\r\n    this.onUpdate = onUpdate\r\n  }\r\n}\r\n", "export function debounce<CB extends (...args: any[]) => void>(\r\n  callback: CB,\r\n  delay: number\r\n) {\r\n  let timer: number | undefined\r\n  return function <T>(this: T, ...args: Parameters<typeof callback>) {\r\n    let context = this\r\n    clearTimeout(timer)\r\n    timer = setTimeout(() => {\r\n      timer = undefined\r\n      callback.apply(context, args)\r\n    }, delay)\r\n  }\r\n}\r\n", "import { debounce } from './debounce'\r\n\r\n/**\r\n * Dimensions class to handle the size of the content and wrapper\r\n *\r\n * @example\r\n * const dimensions = new Dimensions(wrapper, content)\r\n * dimensions.on('resize', (e) => {\r\n *   console.log(e.width, e.height)\r\n * })\r\n */\r\nexport class Dimensions {\r\n  width = 0\r\n  height = 0\r\n  scrollHeight = 0\r\n  scrollWidth = 0\r\n\r\n  // These are instanciated in the constructor as they need information from the options\r\n  private debouncedResize?: (...args: unknown[]) => void\r\n  private wrapperResizeObserver?: ResizeObserver\r\n  private contentResizeObserver?: ResizeObserver\r\n\r\n  constructor(\r\n    private wrapper: HTMLElement | Window | Element,\r\n    private content: HTMLElement | Element,\r\n    { autoResize = true, debounce: debounceValue = 250 } = {}\r\n  ) {\r\n    if (autoResize) {\r\n      this.debouncedResize = debounce(this.resize, debounceValue)\r\n\r\n      if (this.wrapper instanceof Window) {\r\n        window.addEventListener('resize', this.debouncedResize, false)\r\n      } else {\r\n        this.wrapperResizeObserver = new ResizeObserver(this.debouncedResize)\r\n        this.wrapperResizeObserver.observe(this.wrapper)\r\n      }\r\n\r\n      this.contentResizeObserver = new ResizeObserver(this.debouncedResize)\r\n      this.contentResizeObserver.observe(this.content)\r\n    }\r\n\r\n    this.resize()\r\n  }\r\n\r\n  destroy() {\r\n    this.wrapperResizeObserver?.disconnect()\r\n    this.contentResizeObserver?.disconnect()\r\n\r\n    if (this.wrapper === window && this.debouncedResize) {\r\n      window.removeEventListener('resize', this.debouncedResize, false)\r\n    }\r\n  }\r\n\r\n  resize = () => {\r\n    this.onWrapperResize()\r\n    this.onContentResize()\r\n  }\r\n\r\n  onWrapperResize = () => {\r\n    if (this.wrapper instanceof Window) {\r\n      this.width = window.innerWidth\r\n      this.height = window.innerHeight\r\n    } else {\r\n      this.width = this.wrapper.clientWidth\r\n      this.height = this.wrapper.clientHeight\r\n    }\r\n  }\r\n\r\n  onContentResize = () => {\r\n    if (this.wrapper instanceof Window) {\r\n      this.scrollHeight = this.content.scrollHeight\r\n      this.scrollWidth = this.content.scrollWidth\r\n    } else {\r\n      this.scrollHeight = this.wrapper.scrollHeight\r\n      this.scrollWidth = this.wrapper.scrollWidth\r\n    }\r\n  }\r\n\r\n  get limit() {\r\n    return {\r\n      x: this.scrollWidth - this.width,\r\n      y: this.scrollHeight - this.height,\r\n    }\r\n  }\r\n}\r\n", "/**\r\n * Emitter class to handle events\r\n * @example\r\n * const emitter = new Emitter()\r\n * emitter.on('event', (data) => {\r\n *   console.log(data)\r\n * })\r\n * emitter.emit('event', 'data')\r\n */\r\nexport class Emitter {\r\n  private events: Record<\r\n    string,\r\n    Array<(...args: unknown[]) => void> | undefined\r\n  > = {}\r\n\r\n  /**\r\n   * Emit an event with the given data\r\n   * @param event Event name\r\n   * @param args Data to pass to the event handlers\r\n   */\r\n  emit(event: string, ...args: unknown[]) {\r\n    let callbacks = this.events[event] || []\r\n    for (let i = 0, length = callbacks.length; i < length; i++) {\r\n      callbacks[i]?.(...args)\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Add a callback to the event\r\n   * @param event Event name\r\n   * @param cb Callback function\r\n   * @returns Unsubscribe function\r\n   */\r\n  on<CB extends (...args: any[]) => void>(event: string, cb: CB) {\r\n    // Add the callback to the event's callback list, or create a new list with the callback\r\n    this.events[event]?.push(cb) || (this.events[event] = [cb])\r\n\r\n    // Return an unsubscribe function\r\n    return () => {\r\n      this.events[event] = this.events[event]?.filter((i) => cb !== i)\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Remove a callback from the event\r\n   * @param event Event name\r\n   * @param callback Callback function\r\n   */\r\n  off<CB extends (...args: any[]) => void>(event: string, callback: CB) {\r\n    this.events[event] = this.events[event]?.filter((i) => callback !== i)\r\n  }\r\n\r\n  /**\r\n   * Remove all event listeners and clean up\r\n   */\r\n  destroy() {\r\n    this.events = {}\r\n  }\r\n}\r\n", "import { Emitter } from './emitter'\r\nimport type { VirtualScrollCallback } from './types'\r\n\r\nconst LINE_HEIGHT = 100 / 6\r\nconst listenerOptions: AddEventListenerOptions = { passive: false }\r\n\r\nexport class VirtualScroll {\r\n  touchStart = {\r\n    x: 0,\r\n    y: 0,\r\n  }\r\n  lastDelta = {\r\n    x: 0,\r\n    y: 0,\r\n  }\r\n  window = {\r\n    width: 0,\r\n    height: 0,\r\n  }\r\n  private emitter = new Emitter()\r\n\r\n  constructor(\r\n    private element: HTMLElement,\r\n    private options = { wheelMultiplier: 1, touchMultiplier: 1 }\r\n  ) {\r\n    window.addEventListener('resize', this.onWindowResize, false)\r\n    this.onWindowResize()\r\n\r\n    this.element.addEventListener('wheel', this.onWheel, listenerOptions)\r\n    this.element.addEventListener(\r\n      'touchstart',\r\n      this.onTouchStart,\r\n      listenerOptions\r\n    )\r\n    this.element.addEventListener(\r\n      'touchmove',\r\n      this.onTouchMove,\r\n      listenerOptions\r\n    )\r\n    this.element.addEventListener('touchend', this.onTouchEnd, listenerOptions)\r\n  }\r\n\r\n  /**\r\n   * Add an event listener for the given event and callback\r\n   *\r\n   * @param event Event name\r\n   * @param callback Callback function\r\n   */\r\n  on(event: string, callback: VirtualScrollCallback) {\r\n    return this.emitter.on(event, callback)\r\n  }\r\n\r\n  /** Remove all event listeners and clean up */\r\n  destroy() {\r\n    this.emitter.destroy()\r\n\r\n    window.removeEventListener('resize', this.onWindowResize, false)\r\n\r\n    this.element.removeEventListener('wheel', this.onWheel, listenerOptions)\r\n    this.element.removeEventListener(\r\n      'touchstart',\r\n      this.onTouchStart,\r\n      listenerOptions\r\n    )\r\n    this.element.removeEventListener(\r\n      'touchmove',\r\n      this.onTouchMove,\r\n      listenerOptions\r\n    )\r\n    this.element.removeEventListener(\r\n      'touchend',\r\n      this.onTouchEnd,\r\n      listenerOptions\r\n    )\r\n  }\r\n\r\n  /**\r\n   * Event handler for 'touchstart' event\r\n   *\r\n   * @param event Touch event\r\n   */\r\n  onTouchStart = (event: TouchEvent) => {\r\n    // @ts-expect-error - event.targetTouches is not defined\r\n    const { clientX, clientY } = event.targetTouches\r\n      ? event.targetTouches[0]\r\n      : event\r\n\r\n    this.touchStart.x = clientX\r\n    this.touchStart.y = clientY\r\n\r\n    this.lastDelta = {\r\n      x: 0,\r\n      y: 0,\r\n    }\r\n\r\n    this.emitter.emit('scroll', {\r\n      deltaX: 0,\r\n      deltaY: 0,\r\n      event,\r\n    })\r\n  }\r\n\r\n  /** Event handler for 'touchmove' event */\r\n  onTouchMove = (event: TouchEvent) => {\r\n    // @ts-expect-error - event.targetTouches is not defined\r\n    const { clientX, clientY } = event.targetTouches\r\n      ? event.targetTouches[0]\r\n      : event\r\n\r\n    const deltaX = -(clientX - this.touchStart.x) * this.options.touchMultiplier\r\n    const deltaY = -(clientY - this.touchStart.y) * this.options.touchMultiplier\r\n\r\n    this.touchStart.x = clientX\r\n    this.touchStart.y = clientY\r\n\r\n    this.lastDelta = {\r\n      x: deltaX,\r\n      y: deltaY,\r\n    }\r\n\r\n    this.emitter.emit('scroll', {\r\n      deltaX,\r\n      deltaY,\r\n      event,\r\n    })\r\n  }\r\n\r\n  onTouchEnd = (event: TouchEvent) => {\r\n    this.emitter.emit('scroll', {\r\n      deltaX: this.lastDelta.x,\r\n      deltaY: this.lastDelta.y,\r\n      event,\r\n    })\r\n  }\r\n\r\n  /** Event handler for 'wheel' event */\r\n  onWheel = (event: WheelEvent) => {\r\n    let { deltaX, deltaY, deltaMode } = event\r\n\r\n    const multiplierX =\r\n      deltaMode === 1 ? LINE_HEIGHT : deltaMode === 2 ? this.window.width : 1\r\n    const multiplierY =\r\n      deltaMode === 1 ? LINE_HEIGHT : deltaMode === 2 ? this.window.height : 1\r\n\r\n    deltaX *= multiplierX\r\n    deltaY *= multiplierY\r\n\r\n    deltaX *= this.options.wheelMultiplier\r\n    deltaY *= this.options.wheelMultiplier\r\n\r\n    this.emitter.emit('scroll', { deltaX, deltaY, event })\r\n  }\r\n\r\n  onWindowResize = () => {\r\n    this.window = {\r\n      width: window.innerWidth,\r\n      height: window.innerHeight,\r\n    }\r\n  }\r\n}\r\n", "import { version } from '../../../package.json'\nimport { Animate } from './animate'\nimport { Dimensions } from './dimensions'\nimport { Emitter } from './emitter'\nimport { clamp, modulo } from './maths'\nimport type {\n  LenisEvent,\n  LenisOptions,\n  ScrollCallback,\n  Scrolling,\n  ScrollToOptions,\n  UserData,\n  VirtualScrollCallback,\n  VirtualScrollData,\n} from './types'\nimport { VirtualScroll } from './virtual-scroll'\n\n// Technical explanation\n// - listen to 'wheel' events\n// - prevent 'wheel' event to prevent scroll\n// - normalize wheel delta\n// - add delta to targetScroll\n// - animate scroll to targetScroll (smooth context)\n// - if animation is not running, listen to 'scroll' events (native context)\n\ntype OptionalPick<T, F extends keyof T> = Omit<T, F> & Partial<Pick<T, F>>\n\nexport class Lenis {\n  private _isScrolling: Scrolling = false // true when scroll is animating\n  private _isStopped = false // true if user should not be able to scroll - enable/disable programmatically\n  private _isLocked = false // same as isStopped but enabled/disabled when scroll reaches target\n  private _preventNextNativeScrollEvent = false\n  private _resetVelocityTimeout: number | null = null\n  private __rafID: number | null = null\n\n  /**\n   * Whether or not the user is touching the screen\n   */\n  isTouching?: boolean\n  /**\n   * The time in ms since the lenis instance was created\n   */\n  time = 0\n  /**\n   * User data that will be forwarded through the scroll event\n   *\n   * @example\n   * lenis.scrollTo(100, {\n   *   userData: {\n   *     foo: 'bar'\n   *   }\n   * })\n   */\n  userData: UserData = {}\n  /**\n   * The last velocity of the scroll\n   */\n  lastVelocity = 0\n  /**\n   * The current velocity of the scroll\n   */\n  velocity = 0\n  /**\n   * The direction of the scroll\n   */\n  direction: 1 | -1 | 0 = 0\n  /**\n   * The options passed to the lenis instance\n   */\n  options: OptionalPick<\n    Required<LenisOptions>,\n    'duration' | 'prevent' | 'virtualScroll'\n  >\n  /**\n   * The target scroll value\n   */\n  targetScroll: number\n  /**\n   * The animated scroll value\n   */\n  animatedScroll: number\n\n  // These are instanciated here as they don't need information from the options\n  private readonly animate = new Animate()\n  private readonly emitter = new Emitter()\n  // These are instanciated in the constructor as they need information from the options\n  readonly dimensions: Dimensions // This is not private because it's used in the Snap class\n  private readonly virtualScroll: VirtualScroll\n\n  constructor({\n    wrapper = window,\n    content = document.documentElement,\n    eventsTarget = wrapper,\n    smoothWheel = true,\n    syncTouch = false,\n    syncTouchLerp = 0.075,\n    touchInertiaMultiplier = 35,\n    duration, // in seconds\n    easing = (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),\n    lerp = 0.1,\n    infinite = false,\n    orientation = 'vertical', // vertical, horizontal\n    gestureOrientation = 'vertical', // vertical, horizontal, both\n    touchMultiplier = 1,\n    wheelMultiplier = 1,\n    autoResize = true,\n    prevent,\n    virtualScroll,\n    overscroll = true,\n    autoRaf = false,\n    anchors = false,\n    __experimental__naiveDimensions = false,\n  }: LenisOptions = {}) {\n    // Set version\n    window.lenisVersion = version\n\n    // Check if wrapper is <html>, fallback to window\n    if (!wrapper || wrapper === document.documentElement) {\n      wrapper = window\n    }\n\n    // Setup options\n    this.options = {\n      wrapper,\n      content,\n      eventsTarget,\n      smoothWheel,\n      syncTouch,\n      syncTouchLerp,\n      touchInertiaMultiplier,\n      duration,\n      easing,\n      lerp,\n      infinite,\n      gestureOrientation,\n      orientation,\n      touchMultiplier,\n      wheelMultiplier,\n      autoResize,\n      prevent,\n      virtualScroll,\n      overscroll,\n      autoRaf,\n      anchors,\n      __experimental__naiveDimensions,\n    }\n\n    // Setup dimensions instance\n    this.dimensions = new Dimensions(wrapper, content, { autoResize })\n\n    // Setup class name\n    this.updateClassName()\n\n    // Set the initial scroll value for all scroll information\n    this.targetScroll = this.animatedScroll = this.actualScroll\n\n    // Add event listeners\n    this.options.wrapper.addEventListener('scroll', this.onNativeScroll, false)\n\n    this.options.wrapper.addEventListener('scrollend', this.onScrollEnd, {\n      capture: true,\n    })\n\n    if (this.options.anchors && this.options.wrapper === window) {\n      this.options.wrapper.addEventListener(\n        'click',\n        this.onClick as EventListener,\n        false\n      )\n    }\n\n    this.options.wrapper.addEventListener(\n      'pointerdown',\n      this.onPointerDown as EventListener,\n      false\n    )\n\n    // Setup virtual scroll instance\n    this.virtualScroll = new VirtualScroll(eventsTarget as HTMLElement, {\n      touchMultiplier,\n      wheelMultiplier,\n    })\n    this.virtualScroll.on('scroll', this.onVirtualScroll)\n\n    if (this.options.autoRaf) {\n      this.__rafID = requestAnimationFrame(this.raf)\n    }\n  }\n\n  /**\n   * Destroy the lenis instance, remove all event listeners and clean up the class name\n   */\n  destroy() {\n    this.emitter.destroy()\n\n    this.options.wrapper.removeEventListener(\n      'scroll',\n      this.onNativeScroll,\n      false\n    )\n\n    this.options.wrapper.removeEventListener('scrollend', this.onScrollEnd, {\n      capture: true,\n    })\n\n    this.options.wrapper.removeEventListener(\n      'pointerdown',\n      this.onPointerDown as EventListener,\n      false\n    )\n\n    if (this.options.anchors && this.options.wrapper === window) {\n      this.options.wrapper.removeEventListener(\n        'click',\n        this.onClick as EventListener,\n        false\n      )\n    }\n\n    this.virtualScroll.destroy()\n    this.dimensions.destroy()\n\n    this.cleanUpClassName()\n\n    if (this.__rafID) {\n      cancelAnimationFrame(this.__rafID)\n    }\n  }\n\n  /**\n   * Add an event listener for the given event and callback\n   *\n   * @param event Event name\n   * @param callback Callback function\n   * @returns Unsubscribe function\n   */\n  on(event: 'scroll', callback: ScrollCallback): () => void\n  on(event: 'virtual-scroll', callback: VirtualScrollCallback): () => void\n  on(event: LenisEvent, callback: any) {\n    return this.emitter.on(event, callback)\n  }\n\n  /**\n   * Remove an event listener for the given event and callback\n   *\n   * @param event Event name\n   * @param callback Callback function\n   */\n  off(event: 'scroll', callback: ScrollCallback): void\n  off(event: 'virtual-scroll', callback: VirtualScrollCallback): void\n  off(event: LenisEvent, callback: any) {\n    return this.emitter.off(event, callback)\n  }\n\n  private onScrollEnd = (e: Event | CustomEvent) => {\n    if (!(e instanceof CustomEvent)) {\n      if (this.isScrolling === 'smooth' || this.isScrolling === false) {\n        e.stopPropagation()\n      }\n    }\n  }\n\n  private dispatchScrollendEvent = () => {\n    this.options.wrapper.dispatchEvent(\n      new CustomEvent('scrollend', {\n        bubbles: this.options.wrapper === window,\n        // cancelable: false,\n        detail: {\n          lenisScrollEnd: true,\n        },\n      })\n    )\n  }\n\n  private setScroll(scroll: number) {\n    // behavior: 'instant' bypasses the scroll-behavior CSS property\n\n    if (this.isHorizontal) {\n      this.options.wrapper.scrollTo({ left: scroll, behavior: 'instant' })\n    } else {\n      this.options.wrapper.scrollTo({ top: scroll, behavior: 'instant' })\n    }\n  }\n\n  private onClick = (event: PointerEvent | MouseEvent) => {\n    const path = event.composedPath()\n    const anchor = path.find(\n      (node) =>\n        node instanceof HTMLAnchorElement &&\n        (node.getAttribute('href')?.startsWith('#') ||\n          node.getAttribute('href')?.startsWith('/#') ||\n          node.getAttribute('href')?.startsWith('./#'))\n    ) as HTMLAnchorElement | undefined\n    if (anchor) {\n      const id = anchor.getAttribute('href')\n      if (id) {\n        const options =\n          typeof this.options.anchors === 'object' && this.options.anchors\n            ? this.options.anchors\n            : undefined\n        this.scrollTo(`#${id.split('#')[1]}`, options)\n      }\n    }\n  }\n\n  private onPointerDown = (event: PointerEvent | MouseEvent) => {\n    if (event.button === 1) {\n      this.reset()\n    }\n  }\n\n  private onVirtualScroll = (data: VirtualScrollData) => {\n    if (\n      typeof this.options.virtualScroll === 'function' &&\n      this.options.virtualScroll(data) === false\n    )\n      return\n\n    const { deltaX, deltaY, event } = data\n\n    this.emitter.emit('virtual-scroll', { deltaX, deltaY, event })\n\n    // keep zoom feature\n    if (event.ctrlKey) return\n    // @ts-ignore\n    if (event.lenisStopPropagation) return\n\n    const isTouch = event.type.includes('touch')\n    const isWheel = event.type.includes('wheel')\n\n    this.isTouching = event.type === 'touchstart' || event.type === 'touchmove'\n    // if (event.type === 'touchend') {\n    //   console.log('touchend', this.scroll)\n    //   // this.lastVelocity = this.velocity\n    //   // this.velocity = 0\n    //   // this.isScrolling = false\n    //   this.emit({ type: 'touchend' })\n    //   // alert('touchend')\n    //   return\n    // }\n\n    const isClickOrTap = deltaX === 0 && deltaY === 0\n\n    const isTapToStop =\n      this.options.syncTouch &&\n      isTouch &&\n      event.type === 'touchstart' &&\n      isClickOrTap &&\n      !this.isStopped &&\n      !this.isLocked\n\n    if (isTapToStop) {\n      this.reset()\n      return\n    }\n\n    // const isPullToRefresh =\n    //   this.options.gestureOrientation === 'vertical' &&\n    //   this.scroll === 0 &&\n    //   !this.options.infinite &&\n    //   deltaY <= 5 // touch pull to refresh, not reliable yet\n\n    const isUnknownGesture =\n      (this.options.gestureOrientation === 'vertical' && deltaY === 0) ||\n      (this.options.gestureOrientation === 'horizontal' && deltaX === 0)\n\n    if (isClickOrTap || isUnknownGesture) {\n      // console.log('prevent')\n      return\n    }\n\n    // catch if scrolling on nested scroll elements\n    let composedPath = event.composedPath()\n    composedPath = composedPath.slice(0, composedPath.indexOf(this.rootElement)) // remove parents elements\n\n    const prevent = this.options.prevent\n\n    if (\n      !!composedPath.find(\n        (node) =>\n          node instanceof HTMLElement &&\n          ((typeof prevent === 'function' && prevent?.(node)) ||\n            node.hasAttribute?.('data-lenis-prevent') ||\n            (isTouch && node.hasAttribute?.('data-lenis-prevent-touch')) ||\n            (isWheel && node.hasAttribute?.('data-lenis-prevent-wheel')))\n      )\n    )\n      return\n\n    if (this.isStopped || this.isLocked) {\n      event.preventDefault() // this will stop forwarding the event to the parent, this is problematic\n      return\n    }\n\n    const isSmooth =\n      (this.options.syncTouch && isTouch) ||\n      (this.options.smoothWheel && isWheel)\n\n    if (!isSmooth) {\n      this.isScrolling = 'native'\n      this.animate.stop()\n      // @ts-ignore\n      event.lenisStopPropagation = true\n      return\n    }\n\n    let delta = deltaY\n    if (this.options.gestureOrientation === 'both') {\n      delta = Math.abs(deltaY) > Math.abs(deltaX) ? deltaY : deltaX\n    } else if (this.options.gestureOrientation === 'horizontal') {\n      delta = deltaX\n    }\n\n    if (\n      !this.options.overscroll ||\n      this.options.infinite ||\n      (this.options.wrapper !== window &&\n        ((this.animatedScroll > 0 && this.animatedScroll < this.limit) ||\n          (this.animatedScroll === 0 && deltaY > 0) ||\n          (this.animatedScroll === this.limit && deltaY < 0)))\n    ) {\n      // @ts-ignore\n      event.lenisStopPropagation = true\n      // event.stopPropagation()\n    }\n\n    event.preventDefault()\n\n    const isSyncTouch = isTouch && this.options.syncTouch\n    const isTouchEnd = isTouch && event.type === 'touchend'\n\n    const hasTouchInertia = isTouchEnd && Math.abs(delta) > 5\n\n    if (hasTouchInertia) {\n      delta = this.velocity * this.options.touchInertiaMultiplier\n    }\n\n    this.scrollTo(this.targetScroll + delta, {\n      programmatic: false,\n      ...(isSyncTouch\n        ? {\n            lerp: hasTouchInertia ? this.options.syncTouchLerp : 1,\n            // immediate: !hasTouchInertia,\n          }\n        : {\n            lerp: this.options.lerp,\n            duration: this.options.duration,\n            easing: this.options.easing,\n          }),\n    })\n  }\n\n  /**\n   * Force lenis to recalculate the dimensions\n   */\n  resize() {\n    this.dimensions.resize()\n    this.animatedScroll = this.targetScroll = this.actualScroll\n    this.emit()\n  }\n\n  private emit() {\n    this.emitter.emit('scroll', this)\n  }\n\n  private onNativeScroll = () => {\n    if (this._resetVelocityTimeout !== null) {\n      clearTimeout(this._resetVelocityTimeout)\n      this._resetVelocityTimeout = null\n    }\n\n    if (this._preventNextNativeScrollEvent) {\n      this._preventNextNativeScrollEvent = false\n      return\n    }\n\n    if (this.isScrolling === false || this.isScrolling === 'native') {\n      const lastScroll = this.animatedScroll\n      this.animatedScroll = this.targetScroll = this.actualScroll\n      this.lastVelocity = this.velocity\n      this.velocity = this.animatedScroll - lastScroll\n      this.direction = Math.sign(\n        this.animatedScroll - lastScroll\n      ) as Lenis['direction']\n\n      if (!this.isStopped) {\n        this.isScrolling = 'native'\n      }\n\n      this.emit()\n\n      if (this.velocity !== 0) {\n        this._resetVelocityTimeout = setTimeout(() => {\n          this.lastVelocity = this.velocity\n          this.velocity = 0\n          this.isScrolling = false\n          this.emit()\n        }, 400)\n      }\n    }\n  }\n\n  private reset() {\n    this.isLocked = false\n    this.isScrolling = false\n    this.animatedScroll = this.targetScroll = this.actualScroll\n    this.lastVelocity = this.velocity = 0\n    this.animate.stop()\n  }\n\n  /**\n   * Start lenis scroll after it has been stopped\n   */\n  start() {\n    if (!this.isStopped) return\n    this.reset()\n\n    this.isStopped = false\n  }\n\n  /**\n   * Stop lenis scroll\n   */\n  stop() {\n    if (this.isStopped) return\n    this.reset()\n\n    this.isStopped = true\n  }\n\n  /**\n   * RequestAnimationFrame for lenis\n   *\n   * @param time The time in ms from an external clock like `requestAnimationFrame` or Tempus\n   */\n  raf = (time: number) => {\n    const deltaTime = time - (this.time || time)\n    this.time = time\n\n    this.animate.advance(deltaTime * 0.001)\n\n    if (this.options.autoRaf) {\n      this.__rafID = requestAnimationFrame(this.raf)\n    }\n  }\n\n  /**\n   * Scroll to a target value\n   *\n   * @param target The target value to scroll to\n   * @param options The options for the scroll\n   *\n   * @example\n   * lenis.scrollTo(100, {\n   *   offset: 100,\n   *   duration: 1,\n   *   easing: (t) => 1 - Math.cos((t * Math.PI) / 2),\n   *   lerp: 0.1,\n   *   onStart: () => {\n   *     console.log('onStart')\n   *   },\n   *   onComplete: () => {\n   *     console.log('onComplete')\n   *   },\n   * })\n   */\n  scrollTo(\n    target: number | string | HTMLElement,\n    {\n      offset = 0,\n      immediate = false,\n      lock = false,\n      duration = this.options.duration,\n      easing = this.options.easing,\n      lerp = this.options.lerp,\n      onStart,\n      onComplete,\n      force = false, // scroll even if stopped\n      programmatic = true, // called from outside of the class\n      userData,\n    }: ScrollToOptions = {}\n  ) {\n    if ((this.isStopped || this.isLocked) && !force) return\n\n    // keywords\n    if (\n      typeof target === 'string' &&\n      ['top', 'left', 'start'].includes(target)\n    ) {\n      target = 0\n    } else if (\n      typeof target === 'string' &&\n      ['bottom', 'right', 'end'].includes(target)\n    ) {\n      target = this.limit\n    } else {\n      let node\n\n      if (typeof target === 'string') {\n        // CSS selector\n        node = document.querySelector(target)\n      } else if (target instanceof HTMLElement && target?.nodeType) {\n        // Node element\n        node = target\n      }\n\n      if (node) {\n        if (this.options.wrapper !== window) {\n          // nested scroll offset correction\n          const wrapperRect = this.rootElement.getBoundingClientRect()\n          offset -= this.isHorizontal ? wrapperRect.left : wrapperRect.top\n        }\n\n        const rect = node.getBoundingClientRect()\n\n        target =\n          (this.isHorizontal ? rect.left : rect.top) + this.animatedScroll\n      }\n    }\n\n    if (typeof target !== 'number') return\n\n    target += offset\n    target = Math.round(target)\n\n    if (this.options.infinite) {\n      if (programmatic) {\n        this.targetScroll = this.animatedScroll = this.scroll\n      }\n    } else {\n      target = clamp(0, target, this.limit)\n    }\n\n    if (target === this.targetScroll) {\n      onStart?.(this)\n      onComplete?.(this)\n      return\n    }\n\n    this.userData = userData ?? {}\n\n    if (immediate) {\n      this.animatedScroll = this.targetScroll = target\n      this.setScroll(this.scroll)\n      this.reset()\n      this.preventNextNativeScrollEvent()\n      this.emit()\n      onComplete?.(this)\n      this.userData = {}\n\n      requestAnimationFrame(() => {\n        this.dispatchScrollendEvent()\n      })\n      return\n    }\n\n    if (!programmatic) {\n      this.targetScroll = target\n    }\n\n    this.animate.fromTo(this.animatedScroll, target, {\n      duration,\n      easing,\n      lerp,\n      onStart: () => {\n        // started\n        if (lock) this.isLocked = true\n        this.isScrolling = 'smooth'\n        onStart?.(this)\n      },\n      onUpdate: (value: number, completed: boolean) => {\n        this.isScrolling = 'smooth'\n\n        // updated\n        this.lastVelocity = this.velocity\n        this.velocity = value - this.animatedScroll\n        this.direction = Math.sign(this.velocity) as Lenis['direction']\n\n        this.animatedScroll = value\n        this.setScroll(this.scroll)\n\n        if (programmatic) {\n          // wheel during programmatic should stop it\n          this.targetScroll = value\n        }\n\n        if (!completed) this.emit()\n\n        if (completed) {\n          this.reset()\n          this.emit()\n          onComplete?.(this)\n          this.userData = {}\n\n          requestAnimationFrame(() => {\n            this.dispatchScrollendEvent()\n          })\n\n          // avoid emitting event twice\n          this.preventNextNativeScrollEvent()\n        }\n      },\n    })\n  }\n\n  private preventNextNativeScrollEvent() {\n    this._preventNextNativeScrollEvent = true\n\n    requestAnimationFrame(() => {\n      this._preventNextNativeScrollEvent = false\n    })\n  }\n\n  /**\n   * The root element on which lenis is instanced\n   */\n  get rootElement() {\n    return (\n      this.options.wrapper === window\n        ? document.documentElement\n        : this.options.wrapper\n    ) as HTMLElement\n  }\n\n  /**\n   * The limit which is the maximum scroll value\n   */\n  get limit() {\n    if (this.options.__experimental__naiveDimensions) {\n      if (this.isHorizontal) {\n        return this.rootElement.scrollWidth - this.rootElement.clientWidth\n      } else {\n        return this.rootElement.scrollHeight - this.rootElement.clientHeight\n      }\n    } else {\n      return this.dimensions.limit[this.isHorizontal ? 'x' : 'y']\n    }\n  }\n\n  /**\n   * Whether or not the scroll is horizontal\n   */\n  get isHorizontal() {\n    return this.options.orientation === 'horizontal'\n  }\n\n  /**\n   * The actual scroll value\n   */\n  get actualScroll() {\n    // value browser takes into account\n    // it has to be this way because of DOCTYPE declaration\n    const wrapper = this.options.wrapper as Window | HTMLElement\n\n    return this.isHorizontal\n      ? (wrapper as Window).scrollX ?? (wrapper as HTMLElement).scrollLeft\n      : (wrapper as Window).scrollY ?? (wrapper as HTMLElement).scrollTop\n  }\n\n  /**\n   * The current scroll value\n   */\n  get scroll() {\n    return this.options.infinite\n      ? modulo(this.animatedScroll, this.limit)\n      : this.animatedScroll\n  }\n\n  /**\n   * The progress of the scroll relative to the limit\n   */\n  get progress() {\n    // avoid progress to be NaN\n    return this.limit === 0 ? 1 : this.scroll / this.limit\n  }\n\n  /**\n   * Current scroll state\n   */\n  get isScrolling() {\n    return this._isScrolling\n  }\n\n  private set isScrolling(value: Scrolling) {\n    if (this._isScrolling !== value) {\n      this._isScrolling = value\n      this.updateClassName()\n    }\n  }\n\n  /**\n   * Check if lenis is stopped\n   */\n  get isStopped() {\n    return this._isStopped\n  }\n\n  private set isStopped(value: boolean) {\n    if (this._isStopped !== value) {\n      this._isStopped = value\n      this.updateClassName()\n    }\n  }\n\n  /**\n   * Check if lenis is locked\n   */\n  get isLocked() {\n    return this._isLocked\n  }\n\n  private set isLocked(value: boolean) {\n    if (this._isLocked !== value) {\n      this._isLocked = value\n      this.updateClassName()\n    }\n  }\n\n  /**\n   * Check if lenis is smooth scrolling\n   */\n  get isSmooth() {\n    return this.isScrolling === 'smooth'\n  }\n\n  /**\n   * The class name applied to the wrapper element\n   */\n  get className() {\n    let className = 'lenis'\n    if (this.isStopped) className += ' lenis-stopped'\n    if (this.isLocked) className += ' lenis-locked'\n    if (this.isScrolling) className += ' lenis-scrolling'\n    if (this.isScrolling === 'smooth') className += ' lenis-smooth'\n    return className\n  }\n\n  private updateClassName() {\n    this.cleanUpClassName()\n\n    this.rootElement.className =\n      `${this.rootElement.className} ${this.className}`.trim()\n  }\n\n  private cleanUpClassName() {\n    this.rootElement.className = this.rootElement.className\n      .replace(/lenis(-\\w+)?/g, '')\n      .trim()\n  }\n}\n", "import Lenis, { type ScrollCallback } from 'lenis'\r\nimport {\r\n  createContext,\r\n  forwardRef,\r\n  useCallback,\r\n  useEffect,\r\n  useImperativeHandle,\r\n  useRef,\r\n  useState,\r\n} from 'react'\r\nimport { Store } from './store'\r\nimport type { LenisContextValue, LenisProps, LenisRef } from './types'\r\n\r\nexport const LenisContext = createContext<LenisContextValue | null>(null)\r\n\r\n/**\r\n * The root store for the lenis context\r\n *\r\n * This store serves as a fallback for the context if it is not available\r\n * and allows us to use the global lenis instance above a provider\r\n */\r\nexport const rootLenisContextStore = new Store<LenisContextValue | null>(null)\r\n\r\n/**\r\n * React component to setup a Lenis instance\r\n */\r\nexport const ReactLenis = forwardRef<LenisRef, LenisProps>(\r\n  (\r\n    {\r\n      children,\r\n      root = false,\r\n      options = {},\r\n      className,\r\n      autoRaf = true,\r\n      style,\r\n      props,\r\n    }: LenisProps,\r\n    ref\r\n  ) => {\r\n    const wrapperRef = useRef<HTMLDivElement | null>(null)\r\n    const contentRef = useRef<HTMLDivElement | null>(null)\r\n\r\n    const [lenis, setLenis] = useState<Lenis | undefined>(undefined)\r\n\r\n    // Setup ref\r\n    useImperativeHandle(\r\n      ref,\r\n      () => ({\r\n        wrapper: wrapperRef.current,\r\n        content: contentRef.current,\r\n        lenis,\r\n      }),\r\n      [lenis]\r\n    )\r\n\r\n    // Setup lenis instance\r\n    useEffect(() => {\r\n      const lenis = new Lenis({\r\n        ...options,\r\n        ...(!root && {\r\n          wrapper: wrapperRef.current!,\r\n          content: contentRef.current!,\r\n        }),\r\n        autoRaf: options?.autoRaf ?? autoRaf, // this is to avoid breaking the autoRaf prop if it's still used (require breaking change)\r\n      })\r\n\r\n      setLenis(lenis)\r\n\r\n      return () => {\r\n        lenis.destroy()\r\n        setLenis(undefined)\r\n      }\r\n    }, [root, JSON.stringify(options)])\r\n\r\n    // Handle callbacks\r\n    const callbacksRefs = useRef<\r\n      {\r\n        callback: ScrollCallback\r\n        priority: number\r\n      }[]\r\n    >([])\r\n\r\n    const addCallback: LenisContextValue['addCallback'] = useCallback(\r\n      (callback, priority) => {\r\n        callbacksRefs.current.push({ callback, priority })\r\n        callbacksRefs.current.sort((a, b) => a.priority - b.priority)\r\n      },\r\n      []\r\n    )\r\n\r\n    const removeCallback: LenisContextValue['removeCallback'] = useCallback(\r\n      (callback) => {\r\n        callbacksRefs.current = callbacksRefs.current.filter(\r\n          (cb) => cb.callback !== callback\r\n        )\r\n      },\r\n      []\r\n    )\r\n\r\n    // This makes sure to set the global context if the root is true\r\n    useEffect(() => {\r\n      if (root && lenis) {\r\n        rootLenisContextStore.set({ lenis, addCallback, removeCallback })\r\n\r\n        return () => rootLenisContextStore.set(null)\r\n      }\r\n    }, [root, lenis, addCallback, removeCallback])\r\n\r\n    // Setup callback listeners\r\n    useEffect(() => {\r\n      if (!lenis) return\r\n\r\n      const onScroll: ScrollCallback = (data) => {\r\n        for (let i = 0; i < callbacksRefs.current.length; i++) {\r\n          callbacksRefs.current[i]?.callback(data)\r\n        }\r\n      }\r\n\r\n      lenis.on('scroll', onScroll)\r\n\r\n      return () => {\r\n        lenis.off('scroll', onScroll)\r\n      }\r\n    }, [lenis])\r\n\r\n    return (\r\n      <LenisContext.Provider\r\n        value={{ lenis: lenis!, addCallback, removeCallback }}\r\n      >\r\n        {root ? (\r\n          children\r\n        ) : (\r\n          <div ref={wrapperRef} className={className} style={style} {...props}>\r\n            <div ref={contentRef}>{children}</div>\r\n          </div>\r\n        )}\r\n      </LenisContext.Provider>\r\n    )\r\n  }\r\n)\r\n", "import { useEffect, useState } from 'react'\r\n\r\ntype Listener<S> = (state: S) => void\r\n\r\nexport class Store<S> {\r\n  private listeners: Listener<S>[] = []\r\n\r\n  constructor(private state: S) {}\r\n\r\n  set(state: S) {\r\n    this.state = state\r\n\r\n    for (let listener of this.listeners) {\r\n      listener(this.state)\r\n    }\r\n  }\r\n\r\n  subscribe(listener: Listener<S>) {\r\n    this.listeners = [...this.listeners, listener]\r\n    return () => {\r\n      this.listeners = this.listeners.filter((l) => l !== listener)\r\n    }\r\n  }\r\n\r\n  get() {\r\n    return this.state\r\n  }\r\n}\r\n\r\nexport function useStore<S>(store: Store<S>) {\r\n  const [state, setState] = useState(store.get())\r\n\r\n  useEffect(() => {\r\n    return store.subscribe((state) => setState(state))\r\n  }, [store])\r\n\r\n  return state\r\n}\r\n", "import type { ScrollCallback } from 'lenis'\r\nimport { useContext, useEffect } from 'react'\r\nimport { LenisContext, rootLenisContextStore } from './provider'\r\nimport { useStore } from './store'\r\nimport type { LenisContextValue } from './types'\r\n\r\n// Fall back to an empty object if both context and store are not available\r\nconst fallbackContext: Partial<LenisContextValue> = {}\r\n\r\n/**\r\n * Hook to access the Lenis instance and its methods\r\n *\r\n * @example <caption>Scroll callback</caption>\r\n *          useLenis((lenis) => {\r\n *            if (lenis.isScrolling) {\r\n *              console.log('Scrolling...')\r\n *            }\r\n *\r\n *            if (lenis.progress === 1) {\r\n *              console.log('At the end!')\r\n *            }\r\n *          })\r\n *\r\n * @example <caption>Scroll callback with dependencies</caption>\r\n *          useLenis((lenis) => {\r\n *            if (lenis.isScrolling) {\r\n *              console.log('Scrolling...', someDependency)\r\n *            }\r\n *          }, [someDependency])\r\n * @example <caption>Scroll callback with priority</caption>\r\n *          useLenis((lenis) => {\r\n *            if (lenis.isScrolling) {\r\n *              console.log('Scrolling...')\r\n *            }\r\n *          }, [], 1)\r\n * @example <caption>Instance access</caption>\r\n *          const lenis = useLenis()\r\n *\r\n *          handleClick() {\r\n *            lenis.scrollTo(100, {\r\n *              lerp: 0.1,\r\n *              duration: 1,\r\n *              easing: (t) => t,\r\n *              onComplete: () => {\r\n *                console.log('Complete!')\r\n *              }\r\n *            })\r\n *          }\r\n */\r\nexport function useLenis(\r\n  callback?: ScrollCallback,\r\n  deps: any[] = [],\r\n  priority = 0\r\n) {\r\n  // Try to get the lenis instance from the context first\r\n  const localContext = useContext(LenisContext)\r\n  // Fall back to the root store if the context is not available\r\n  const rootContext = useStore(rootLenisContextStore)\r\n  // Fall back to the fallback context if all else fails\r\n  const currentContext = localContext ?? rootContext ?? fallbackContext\r\n\r\n  const { lenis, addCallback, removeCallback } = currentContext\r\n\r\n  useEffect(() => {\r\n    if (!callback || !addCallback || !removeCallback || !lenis) return\r\n\r\n    addCallback(callback, priority)\r\n    callback(lenis)\r\n\r\n    return () => {\r\n      removeCallback(callback)\r\n    }\r\n  }, [lenis, addCallback, removeCallback, priority, ...deps])\r\n\r\n  return lenis\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;AAEE,IAAA,UAAW;ACMN,SAAS,MAAM,KAAa,OAAe,KAAa;AAC7D,SAAO,KAAK,IAAI,KAAK,KAAK,IAAI,OAAO,GAAG,CAAC;AAC3C;AAqBO,SAAS,KAAK,GAAW,GAAW,GAAW;AACpD,UAAQ,IAAI,KAAK,IAAI,IAAI;AAC3B;AAYO,SAAS,KAAK,GAAW,GAAW,QAAgB,WAAmB;AAC5E,SAAO,KAAK,GAAG,GAAG,IAAI,KAAK,IAAI,CAAC,SAAS,SAAS,CAAC;AACrD;AAUO,SAAS,OAAO,GAAW,GAAW;AAC3C,UAAS,IAAI,IAAK,KAAK;AACzB;AChDO,IAAM,UAAN,MAAc;EAAd;AACL,qCAAY;AACZ,iCAAQ;AACR,gCAAO;AACP,8BAAK;AACL,uCAAc;AAGd;;AACA;AACA;AACA;;;;;;;EAOA,QAAQ,WAAmB;;AACzB,QAAI,CAAC,KAAK,UAAW;AAErB,QAAI,YAAY;AAEhB,QAAI,KAAK,YAAY,KAAK,QAAQ;AAChC,WAAK,eAAe;AACpB,YAAM,iBAAiB,MAAM,GAAG,KAAK,cAAc,KAAK,UAAU,CAAC;AAEnE,kBAAY,kBAAkB;AAC9B,YAAM,gBAAgB,YAAY,IAAI,KAAK,OAAO,cAAc;AAChE,WAAK,QAAQ,KAAK,QAAQ,KAAK,KAAK,KAAK,QAAQ;IACnD,WAAW,KAAK,MAAM;AACpB,WAAK,QAAQ,KAAK,KAAK,OAAO,KAAK,IAAI,KAAK,OAAO,IAAI,SAAS;AAChE,UAAI,KAAK,MAAM,KAAK,KAAK,MAAM,KAAK,IAAI;AACtC,aAAK,QAAQ,KAAK;AAClB,oBAAY;MACd;IACF,OAAO;AAEL,WAAK,QAAQ,KAAK;AAClB,kBAAY;IACd;AAEA,QAAI,WAAW;AACb,WAAK,KAAK;IACZ;AAGA,eAAK,aAAL,8BAAgB,KAAK,OAAO;EAC9B;;EAGA,OAAO;AACL,SAAK,YAAY;EACnB;;;;;;;;;EAUA,OACE,MACA,IACA,EAAE,MAAAA,OAAM,UAAU,QAAQ,SAAS,SAAS,GAC5C;AACA,SAAK,OAAO,KAAK,QAAQ;AACzB,SAAK,KAAK;AACV,SAAK,OAAOA;AACZ,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,cAAc;AACnB,SAAK,YAAY;AAEjB;AACA,SAAK,WAAW;EAClB;AACF;AC1FO,SAAS,SACd,UACA,OACA;AACA,MAAI;AACJ,SAAO,YAAyB,MAAmC;AACjE,QAAI,UAAU;AACd,iBAAa,KAAK;AAClB,YAAQ,WAAW,MAAM;AACvB,cAAQ;AACR,eAAS,MAAM,SAAS,IAAI;IAC9B,GAAG,KAAK;EACV;AACF;ACFO,IAAM,aAAN,MAAiB;EAWtB,YACU,SACA,SACR,EAAE,aAAa,MAAM,UAAU,gBAAgB,IAAI,IAAI,CAAC,GACxD;AAdF,iCAAQ;AACR,kCAAS;AACT,wCAAe;AACf,uCAAc;AAGN;;AACA;AACA;AAiCR,kCAAS,MAAM;AACb,WAAK,gBAAgB;AACrB,WAAK,gBAAgB;IACvB;AAEA,2CAAkB,MAAM;AACtB,UAAI,KAAK,mBAAmB,QAAQ;AAClC,aAAK,QAAQ,OAAO;AACpB,aAAK,SAAS,OAAO;MACvB,OAAO;AACL,aAAK,QAAQ,KAAK,QAAQ;AAC1B,aAAK,SAAS,KAAK,QAAQ;MAC7B;IACF;AAEA,2CAAkB,MAAM;AACtB,UAAI,KAAK,mBAAmB,QAAQ;AAClC,aAAK,eAAe,KAAK,QAAQ;AACjC,aAAK,cAAc,KAAK,QAAQ;MAClC,OAAO;AACL,aAAK,eAAe,KAAK,QAAQ;AACjC,aAAK,cAAc,KAAK,QAAQ;MAClC;IACF;AArDU,SAAA,UAAA;AACA,SAAA,UAAA;AAGR,QAAI,YAAY;AACd,WAAK,kBAAkB,SAAS,KAAK,QAAQ,aAAa;AAE1D,UAAI,KAAK,mBAAmB,QAAQ;AAClC,eAAO,iBAAiB,UAAU,KAAK,iBAAiB,KAAK;MAC/D,OAAO;AACL,aAAK,wBAAwB,IAAI,eAAe,KAAK,eAAe;AACpE,aAAK,sBAAsB,QAAQ,KAAK,OAAO;MACjD;AAEA,WAAK,wBAAwB,IAAI,eAAe,KAAK,eAAe;AACpE,WAAK,sBAAsB,QAAQ,KAAK,OAAO;IACjD;AAEA,SAAK,OAAO;EACd;EAEA,UAAU;;AACR,eAAK,0BAAL,mBAA4B;AAC5B,eAAK,0BAAL,mBAA4B;AAE5B,QAAI,KAAK,YAAY,UAAU,KAAK,iBAAiB;AACnD,aAAO,oBAAoB,UAAU,KAAK,iBAAiB,KAAK;IAClE;EACF;EA2BA,IAAI,QAAQ;AACV,WAAO;MACL,GAAG,KAAK,cAAc,KAAK;MAC3B,GAAG,KAAK,eAAe,KAAK;IAC9B;EACF;AACF;AC3EO,IAAM,UAAN,MAAc;EAAd;AACG,kCAGJ,CAAC;;;;;;;EAOL,KAAK,UAAkB,MAAiB;;AACtC,QAAI,YAAY,KAAK,OAAO,KAAK,KAAK,CAAC;AACvC,aAAS,IAAI,GAAG,SAAS,UAAU,QAAQ,IAAI,QAAQ,KAAK;AAC1D,sBAAU,OAAV,mCAAe,GAAG;IACpB;EACF;;;;;;;EAQA,GAAwC,OAAe,IAAQ;;AAE7D,gBAAK,OAAO,KAAK,MAAjB,mBAAoB,KAAK,SAAQ,KAAK,OAAO,KAAK,IAAI,CAAC,EAAE;AAGzD,WAAO,MAAM;;AACX,WAAK,OAAO,KAAK,KAAIC,MAAA,KAAK,OAAO,KAAK,MAAjB,gBAAAA,IAAoB,OAAO,CAAC,MAAM,OAAO;IAChE;EACF;;;;;;EAOA,IAAyC,OAAe,UAAc;;AACpE,SAAK,OAAO,KAAK,KAAI,UAAK,OAAO,KAAK,MAAjB,mBAAoB,OAAO,CAAC,MAAM,aAAa;EACtE;;;;EAKA,UAAU;AACR,SAAK,SAAS,CAAC;EACjB;AACF;ACvDA,IAAM,cAAc,MAAM;AAC1B,IAAM,kBAA2C,EAAE,SAAS,MAAM;AAE3D,IAAM,gBAAN,MAAoB;EAezB,YACU,SACA,UAAU,EAAE,iBAAiB,GAAG,iBAAiB,EAAE,GAC3D;AAjBF,sCAAa;MACX,GAAG;MACH,GAAG;IACL;AACA,qCAAY;MACV,GAAG;MACH,GAAG;IACL;AACA,kCAAS;MACP,OAAO;MACP,QAAQ;IACV;AACQ,mCAAU,IAAI,QAAQ;AA8D9B;;;;;wCAAe,CAAC,UAAsB;AAEpC,YAAM,EAAE,SAAS,QAAQ,IAAI,MAAM,gBAC/B,MAAM,cAAc,CAAC,IACrB;AAEJ,WAAK,WAAW,IAAI;AACpB,WAAK,WAAW,IAAI;AAEpB,WAAK,YAAY;QACf,GAAG;QACH,GAAG;MACL;AAEA,WAAK,QAAQ,KAAK,UAAU;QAC1B,QAAQ;QACR,QAAQ;QACR;MACF,CAAC;IACH;AAGA;uCAAc,CAAC,UAAsB;AAEnC,YAAM,EAAE,SAAS,QAAQ,IAAI,MAAM,gBAC/B,MAAM,cAAc,CAAC,IACrB;AAEJ,YAAM,SAAS,EAAE,UAAU,KAAK,WAAW,KAAK,KAAK,QAAQ;AAC7D,YAAM,SAAS,EAAE,UAAU,KAAK,WAAW,KAAK,KAAK,QAAQ;AAE7D,WAAK,WAAW,IAAI;AACpB,WAAK,WAAW,IAAI;AAEpB,WAAK,YAAY;QACf,GAAG;QACH,GAAG;MACL;AAEA,WAAK,QAAQ,KAAK,UAAU;QAC1B;QACA;QACA;MACF,CAAC;IACH;AAEA,sCAAa,CAAC,UAAsB;AAClC,WAAK,QAAQ,KAAK,UAAU;QAC1B,QAAQ,KAAK,UAAU;QACvB,QAAQ,KAAK,UAAU;QACvB;MACF,CAAC;IACH;AAGA;mCAAU,CAAC,UAAsB;AAC/B,UAAI,EAAE,QAAQ,QAAQ,UAAU,IAAI;AAEpC,YAAM,cACJ,cAAc,IAAI,cAAc,cAAc,IAAI,KAAK,OAAO,QAAQ;AACxE,YAAM,cACJ,cAAc,IAAI,cAAc,cAAc,IAAI,KAAK,OAAO,SAAS;AAEzE,gBAAU;AACV,gBAAU;AAEV,gBAAU,KAAK,QAAQ;AACvB,gBAAU,KAAK,QAAQ;AAEvB,WAAK,QAAQ,KAAK,UAAU,EAAE,QAAQ,QAAQ,MAAM,CAAC;IACvD;AAEA,0CAAiB,MAAM;AACrB,WAAK,SAAS;QACZ,OAAO,OAAO;QACd,QAAQ,OAAO;MACjB;IACF;AAxIU,SAAA,UAAA;AACA,SAAA,UAAA;AAER,WAAO,iBAAiB,UAAU,KAAK,gBAAgB,KAAK;AAC5D,SAAK,eAAe;AAEpB,SAAK,QAAQ,iBAAiB,SAAS,KAAK,SAAS,eAAe;AACpE,SAAK,QAAQ;MACX;MACA,KAAK;MACL;IACF;AACA,SAAK,QAAQ;MACX;MACA,KAAK;MACL;IACF;AACA,SAAK,QAAQ,iBAAiB,YAAY,KAAK,YAAY,eAAe;EAC5E;;;;;;;EAQA,GAAG,OAAe,UAAiC;AACjD,WAAO,KAAK,QAAQ,GAAG,OAAO,QAAQ;EACxC;;EAGA,UAAU;AACR,SAAK,QAAQ,QAAQ;AAErB,WAAO,oBAAoB,UAAU,KAAK,gBAAgB,KAAK;AAE/D,SAAK,QAAQ,oBAAoB,SAAS,KAAK,SAAS,eAAe;AACvE,SAAK,QAAQ;MACX;MACA,KAAK;MACL;IACF;AACA,SAAK,QAAQ;MACX;MACA,KAAK;MACL;IACF;AACA,SAAK,QAAQ;MACX;MACA,KAAK;MACL;IACF;EACF;AAqFF;ACpIO,IAAM,QAAN,MAAY;EA8DjB,YAAY;IACV,UAAU;IACV,UAAU,SAAS;IACnB,eAAe;IACf,cAAc;IACd,YAAY;IACZ,gBAAgB;IAChB,yBAAyB;IACzB;;IACA,SAAS,CAAC,MAAM,KAAK,IAAI,GAAG,QAAQ,KAAK,IAAI,GAAG,MAAM,CAAC,CAAC;IACxD,MAAAD,QAAO;IACP,WAAW;IACX,cAAc;;IACd,qBAAqB;;IACrB,kBAAkB;IAClB,kBAAkB;IAClB,aAAa;IACb;IACA;IACA,aAAa;IACb,UAAU;IACV,UAAU;IACV,kCAAkC;EACpC,IAAkB,CAAC,GAAG;AApFd,wCAA0B;AAC1B;sCAAa;AACb;qCAAY;AACZ;yDAAgC;AAChC,iDAAuC;AACvC,mCAAyB;AAKjC;;;;AAIA;;;gCAAO;AAWP;;;;;;;;;;oCAAqB,CAAC;AAItB;;;wCAAe;AAIf;;;oCAAW;AAIX;;;qCAAwB;AAIxB;;;;AAOA;;;;AAIA;;;;AAGiB;mCAAU,IAAI,QAAQ;AACtB,mCAAU,IAAI,QAAQ;AAE9B;;AACQ;;AAuKT,uCAAc,CAAC,MAA2B;AAChD,UAAI,EAAE,aAAa,cAAc;AAC/B,YAAI,KAAK,gBAAgB,YAAY,KAAK,gBAAgB,OAAO;AAC/D,YAAE,gBAAgB;QACpB;MACF;IACF;AAEQ,kDAAyB,MAAM;AACrC,WAAK,QAAQ,QAAQ;QACnB,IAAI,YAAY,aAAa;UAC3B,SAAS,KAAK,QAAQ,YAAY;;UAElC,QAAQ;YACN,gBAAgB;UAClB;QACF,CAAC;MACH;IACF;AAYQ,mCAAU,CAAC,UAAqC;AACtD,YAAM,OAAO,MAAM,aAAa;AAChC,YAAM,SAAS,KAAK;QAClB,CAAC,SAAA;;AACC,iCAAgB,wBACf,UAAK,aAAa,MAAM,MAAxB,mBAA2B,WAAW,WACrC,UAAK,aAAa,MAAM,MAAxB,mBAA2B,WAAW,YACtC,UAAK,aAAa,MAAM,MAAxB,mBAA2B,WAAW;;MAC5C;AACA,UAAI,QAAQ;AACV,cAAM,KAAK,OAAO,aAAa,MAAM;AACrC,YAAI,IAAI;AACN,gBAAM,UACJ,OAAO,KAAK,QAAQ,YAAY,YAAY,KAAK,QAAQ,UACrD,KAAK,QAAQ,UACb;AACN,eAAK,SAAS,IAAI,GAAG,MAAM,GAAG,EAAE,CAAC,CAAC,IAAI,OAAO;QAC/C;MACF;IACF;AAEQ,yCAAgB,CAAC,UAAqC;AAC5D,UAAI,MAAM,WAAW,GAAG;AACtB,aAAK,MAAM;MACb;IACF;AAEQ,2CAAkB,CAAC,SAA4B;AACrD,UACE,OAAO,KAAK,QAAQ,kBAAkB,cACtC,KAAK,QAAQ,cAAc,IAAI,MAAM;AAErC;AAEF,YAAM,EAAE,QAAQ,QAAQ,MAAM,IAAI;AAElC,WAAK,QAAQ,KAAK,kBAAkB,EAAE,QAAQ,QAAQ,MAAM,CAAC;AAG7D,UAAI,MAAM,QAAS;AAEnB,UAAI,MAAM,qBAAsB;AAEhC,YAAM,UAAU,MAAM,KAAK,SAAS,OAAO;AAC3C,YAAM,UAAU,MAAM,KAAK,SAAS,OAAO;AAE3C,WAAK,aAAa,MAAM,SAAS,gBAAgB,MAAM,SAAS;AAWhE,YAAM,eAAe,WAAW,KAAK,WAAW;AAEhD,YAAM,cACJ,KAAK,QAAQ,aACb,WACA,MAAM,SAAS,gBACf,gBACA,CAAC,KAAK,aACN,CAAC,KAAK;AAER,UAAI,aAAa;AACf,aAAK,MAAM;AACX;MACF;AAQA,YAAM,mBACH,KAAK,QAAQ,uBAAuB,cAAc,WAAW,KAC7D,KAAK,QAAQ,uBAAuB,gBAAgB,WAAW;AAElE,UAAI,gBAAgB,kBAAkB;AAEpC;MACF;AAGA,UAAI,eAAe,MAAM,aAAa;AACtC,qBAAe,aAAa,MAAM,GAAG,aAAa,QAAQ,KAAK,WAAW,CAAC;AAE3E,YAAM,UAAU,KAAK,QAAQ;AAE7B,UACE,CAAC,CAAC,aAAa;QACb,CAAC,SAAA;;AACC,iCAAgB,gBACd,OAAO,YAAY,eAAc,mCAAU,YAC3C,UAAK,iBAAL,8BAAoB,0BACnB,aAAW,UAAK,iBAAL,8BAAoB,gCAC/B,aAAW,UAAK,iBAAL,8BAAoB;;MACtC;AAEA;AAEF,UAAI,KAAK,aAAa,KAAK,UAAU;AACnC,cAAM,eAAe;AACrB;MACF;AAEA,YAAM,WACH,KAAK,QAAQ,aAAa,WAC1B,KAAK,QAAQ,eAAe;AAE/B,UAAI,CAAC,UAAU;AACb,aAAK,cAAc;AACnB,aAAK,QAAQ,KAAK;AAElB,cAAM,uBAAuB;AAC7B;MACF;AAEA,UAAI,QAAQ;AACZ,UAAI,KAAK,QAAQ,uBAAuB,QAAQ;AAC9C,gBAAQ,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM,IAAI,SAAS;MACzD,WAAW,KAAK,QAAQ,uBAAuB,cAAc;AAC3D,gBAAQ;MACV;AAEA,UACE,CAAC,KAAK,QAAQ,cACd,KAAK,QAAQ,YACZ,KAAK,QAAQ,YAAY,WACtB,KAAK,iBAAiB,KAAK,KAAK,iBAAiB,KAAK,SACrD,KAAK,mBAAmB,KAAK,SAAS,KACtC,KAAK,mBAAmB,KAAK,SAAS,SAAS,IACpD;AAEA,cAAM,uBAAuB;MAE/B;AAEA,YAAM,eAAe;AAErB,YAAM,cAAc,WAAW,KAAK,QAAQ;AAC5C,YAAM,aAAa,WAAW,MAAM,SAAS;AAE7C,YAAM,kBAAkB,cAAc,KAAK,IAAI,KAAK,IAAI;AAExD,UAAI,iBAAiB;AACnB,gBAAQ,KAAK,WAAW,KAAK,QAAQ;MACvC;AAEA,WAAK,SAAS,KAAK,eAAe,OAAO;QACvC,cAAc;QACd,GAAI,cACA;UACE,MAAM,kBAAkB,KAAK,QAAQ,gBAAgB;;QAEvD,IACA;UACE,MAAM,KAAK,QAAQ;UACnB,UAAU,KAAK,QAAQ;UACvB,QAAQ,KAAK,QAAQ;QACvB;MACN,CAAC;IACH;AAeQ,0CAAiB,MAAM;AAC7B,UAAI,KAAK,0BAA0B,MAAM;AACvC,qBAAa,KAAK,qBAAqB;AACvC,aAAK,wBAAwB;MAC/B;AAEA,UAAI,KAAK,+BAA+B;AACtC,aAAK,gCAAgC;AACrC;MACF;AAEA,UAAI,KAAK,gBAAgB,SAAS,KAAK,gBAAgB,UAAU;AAC/D,cAAM,aAAa,KAAK;AACxB,aAAK,iBAAiB,KAAK,eAAe,KAAK;AAC/C,aAAK,eAAe,KAAK;AACzB,aAAK,WAAW,KAAK,iBAAiB;AACtC,aAAK,YAAY,KAAK;UACpB,KAAK,iBAAiB;QACxB;AAEA,YAAI,CAAC,KAAK,WAAW;AACnB,eAAK,cAAc;QACrB;AAEA,aAAK,KAAK;AAEV,YAAI,KAAK,aAAa,GAAG;AACvB,eAAK,wBAAwB,WAAW,MAAM;AAC5C,iBAAK,eAAe,KAAK;AACzB,iBAAK,WAAW;AAChB,iBAAK,cAAc;AACnB,iBAAK,KAAK;UACZ,GAAG,GAAG;QACR;MACF;IACF;AAmCA;;;;;+BAAM,CAAC,SAAiB;AACtB,YAAM,YAAY,QAAQ,KAAK,QAAQ;AACvC,WAAK,OAAO;AAEZ,WAAK,QAAQ,QAAQ,YAAY,IAAK;AAEtC,UAAI,KAAK,QAAQ,SAAS;AACxB,aAAK,UAAU,sBAAsB,KAAK,GAAG;MAC/C;IACF;AA9aE,WAAO,eAAe;AAGtB,QAAI,CAAC,WAAW,YAAY,SAAS,iBAAiB;AACpD,gBAAU;IACZ;AAGA,SAAK,UAAU;MACb;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,MAAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACF;AAGA,SAAK,aAAa,IAAI,WAAW,SAAS,SAAS,EAAE,WAAW,CAAC;AAGjE,SAAK,gBAAgB;AAGrB,SAAK,eAAe,KAAK,iBAAiB,KAAK;AAG/C,SAAK,QAAQ,QAAQ,iBAAiB,UAAU,KAAK,gBAAgB,KAAK;AAE1E,SAAK,QAAQ,QAAQ,iBAAiB,aAAa,KAAK,aAAa;MACnE,SAAS;IACX,CAAC;AAED,QAAI,KAAK,QAAQ,WAAW,KAAK,QAAQ,YAAY,QAAQ;AAC3D,WAAK,QAAQ,QAAQ;QACnB;QACA,KAAK;QACL;MACF;IACF;AAEA,SAAK,QAAQ,QAAQ;MACnB;MACA,KAAK;MACL;IACF;AAGA,SAAK,gBAAgB,IAAI,cAAc,cAA6B;MAClE;MACA;IACF,CAAC;AACD,SAAK,cAAc,GAAG,UAAU,KAAK,eAAe;AAEpD,QAAI,KAAK,QAAQ,SAAS;AACxB,WAAK,UAAU,sBAAsB,KAAK,GAAG;IAC/C;EACF;;;;EAKA,UAAU;AACR,SAAK,QAAQ,QAAQ;AAErB,SAAK,QAAQ,QAAQ;MACnB;MACA,KAAK;MACL;IACF;AAEA,SAAK,QAAQ,QAAQ,oBAAoB,aAAa,KAAK,aAAa;MACtE,SAAS;IACX,CAAC;AAED,SAAK,QAAQ,QAAQ;MACnB;MACA,KAAK;MACL;IACF;AAEA,QAAI,KAAK,QAAQ,WAAW,KAAK,QAAQ,YAAY,QAAQ;AAC3D,WAAK,QAAQ,QAAQ;QACnB;QACA,KAAK;QACL;MACF;IACF;AAEA,SAAK,cAAc,QAAQ;AAC3B,SAAK,WAAW,QAAQ;AAExB,SAAK,iBAAiB;AAEtB,QAAI,KAAK,SAAS;AAChB,2BAAqB,KAAK,OAAO;IACnC;EACF;EAWA,GAAG,OAAmB,UAAe;AACnC,WAAO,KAAK,QAAQ,GAAG,OAAO,QAAQ;EACxC;EAUA,IAAI,OAAmB,UAAe;AACpC,WAAO,KAAK,QAAQ,IAAI,OAAO,QAAQ;EACzC;EAsBQ,UAAU,QAAgB;AAGhC,QAAI,KAAK,cAAc;AACrB,WAAK,QAAQ,QAAQ,SAAS,EAAE,MAAM,QAAQ,UAAU,UAAU,CAAC;IACrE,OAAO;AACL,WAAK,QAAQ,QAAQ,SAAS,EAAE,KAAK,QAAQ,UAAU,UAAU,CAAC;IACpE;EACF;;;;EA6KA,SAAS;AACP,SAAK,WAAW,OAAO;AACvB,SAAK,iBAAiB,KAAK,eAAe,KAAK;AAC/C,SAAK,KAAK;EACZ;EAEQ,OAAO;AACb,SAAK,QAAQ,KAAK,UAAU,IAAI;EAClC;EAuCQ,QAAQ;AACd,SAAK,WAAW;AAChB,SAAK,cAAc;AACnB,SAAK,iBAAiB,KAAK,eAAe,KAAK;AAC/C,SAAK,eAAe,KAAK,WAAW;AACpC,SAAK,QAAQ,KAAK;EACpB;;;;EAKA,QAAQ;AACN,QAAI,CAAC,KAAK,UAAW;AACrB,SAAK,MAAM;AAEX,SAAK,YAAY;EACnB;;;;EAKA,OAAO;AACL,QAAI,KAAK,UAAW;AACpB,SAAK,MAAM;AAEX,SAAK,YAAY;EACnB;;;;;;;;;;;;;;;;;;;;;EAsCA,SACE,QACA;IACE,SAAS;IACT,YAAY;IACZ,OAAO;IACP,WAAW,KAAK,QAAQ;IACxB,SAAS,KAAK,QAAQ;IACtB,MAAAA,QAAO,KAAK,QAAQ;IACpB;IACA;IACA,QAAQ;;IACR,eAAe;;IACf;EACF,IAAqB,CAAC,GACtB;AACA,SAAK,KAAK,aAAa,KAAK,aAAa,CAAC,MAAO;AAGjD,QACE,OAAO,WAAW,YAClB,CAAC,OAAO,QAAQ,OAAO,EAAE,SAAS,MAAM,GACxC;AACA,eAAS;IACX,WACE,OAAO,WAAW,YAClB,CAAC,UAAU,SAAS,KAAK,EAAE,SAAS,MAAM,GAC1C;AACA,eAAS,KAAK;IAChB,OAAO;AACL,UAAI;AAEJ,UAAI,OAAO,WAAW,UAAU;AAE9B,eAAO,SAAS,cAAc,MAAM;MACtC,WAAW,kBAAkB,gBAAe,iCAAQ,WAAU;AAE5D,eAAO;MACT;AAEA,UAAI,MAAM;AACR,YAAI,KAAK,QAAQ,YAAY,QAAQ;AAEnC,gBAAM,cAAc,KAAK,YAAY,sBAAsB;AAC3D,oBAAU,KAAK,eAAe,YAAY,OAAO,YAAY;QAC/D;AAEA,cAAM,OAAO,KAAK,sBAAsB;AAExC,kBACG,KAAK,eAAe,KAAK,OAAO,KAAK,OAAO,KAAK;MACtD;IACF;AAEA,QAAI,OAAO,WAAW,SAAU;AAEhC,cAAU;AACV,aAAS,KAAK,MAAM,MAAM;AAE1B,QAAI,KAAK,QAAQ,UAAU;AACzB,UAAI,cAAc;AAChB,aAAK,eAAe,KAAK,iBAAiB,KAAK;MACjD;IACF,OAAO;AACL,eAAS,MAAM,GAAG,QAAQ,KAAK,KAAK;IACtC;AAEA,QAAI,WAAW,KAAK,cAAc;AAChC,yCAAU;AACV,+CAAa;AACb;IACF;AAEA,SAAK,WAAW,YAAY,CAAC;AAE7B,QAAI,WAAW;AACb,WAAK,iBAAiB,KAAK,eAAe;AAC1C,WAAK,UAAU,KAAK,MAAM;AAC1B,WAAK,MAAM;AACX,WAAK,6BAA6B;AAClC,WAAK,KAAK;AACV,+CAAa;AACb,WAAK,WAAW,CAAC;AAEjB,4BAAsB,MAAM;AAC1B,aAAK,uBAAuB;MAC9B,CAAC;AACD;IACF;AAEA,QAAI,CAAC,cAAc;AACjB,WAAK,eAAe;IACtB;AAEA,SAAK,QAAQ,OAAO,KAAK,gBAAgB,QAAQ;MAC/C;MACA;MACA,MAAAA;MACA,SAAS,MAAM;AAEb,YAAI,KAAM,MAAK,WAAW;AAC1B,aAAK,cAAc;AACnB,2CAAU;MACZ;MACA,UAAU,CAAC,OAAe,cAAuB;AAC/C,aAAK,cAAc;AAGnB,aAAK,eAAe,KAAK;AACzB,aAAK,WAAW,QAAQ,KAAK;AAC7B,aAAK,YAAY,KAAK,KAAK,KAAK,QAAQ;AAExC,aAAK,iBAAiB;AACtB,aAAK,UAAU,KAAK,MAAM;AAE1B,YAAI,cAAc;AAEhB,eAAK,eAAe;QACtB;AAEA,YAAI,CAAC,UAAW,MAAK,KAAK;AAE1B,YAAI,WAAW;AACb,eAAK,MAAM;AACX,eAAK,KAAK;AACV,mDAAa;AACb,eAAK,WAAW,CAAC;AAEjB,gCAAsB,MAAM;AAC1B,iBAAK,uBAAuB;UAC9B,CAAC;AAGD,eAAK,6BAA6B;QACpC;MACF;IACF,CAAC;EACH;EAEQ,+BAA+B;AACrC,SAAK,gCAAgC;AAErC,0BAAsB,MAAM;AAC1B,WAAK,gCAAgC;IACvC,CAAC;EACH;;;;EAKA,IAAI,cAAc;AAChB,WACE,KAAK,QAAQ,YAAY,SACrB,SAAS,kBACT,KAAK,QAAQ;EAErB;;;;EAKA,IAAI,QAAQ;AACV,QAAI,KAAK,QAAQ,iCAAiC;AAChD,UAAI,KAAK,cAAc;AACrB,eAAO,KAAK,YAAY,cAAc,KAAK,YAAY;MACzD,OAAO;AACL,eAAO,KAAK,YAAY,eAAe,KAAK,YAAY;MAC1D;IACF,OAAO;AACL,aAAO,KAAK,WAAW,MAAM,KAAK,eAAe,MAAM,GAAG;IAC5D;EACF;;;;EAKA,IAAI,eAAe;AACjB,WAAO,KAAK,QAAQ,gBAAgB;EACtC;;;;EAKA,IAAI,eAAe;AAGjB,UAAM,UAAU,KAAK,QAAQ;AAE7B,WAAO,KAAK,eACP,QAAmB,WAAY,QAAwB,aACvD,QAAmB,WAAY,QAAwB;EAC9D;;;;EAKA,IAAI,SAAS;AACX,WAAO,KAAK,QAAQ,WAChB,OAAO,KAAK,gBAAgB,KAAK,KAAK,IACtC,KAAK;EACX;;;;EAKA,IAAI,WAAW;AAEb,WAAO,KAAK,UAAU,IAAI,IAAI,KAAK,SAAS,KAAK;EACnD;;;;EAKA,IAAI,cAAc;AAChB,WAAO,KAAK;EACd;EAEA,IAAY,YAAY,OAAkB;AACxC,QAAI,KAAK,iBAAiB,OAAO;AAC/B,WAAK,eAAe;AACpB,WAAK,gBAAgB;IACvB;EACF;;;;EAKA,IAAI,YAAY;AACd,WAAO,KAAK;EACd;EAEA,IAAY,UAAU,OAAgB;AACpC,QAAI,KAAK,eAAe,OAAO;AAC7B,WAAK,aAAa;AAClB,WAAK,gBAAgB;IACvB;EACF;;;;EAKA,IAAI,WAAW;AACb,WAAO,KAAK;EACd;EAEA,IAAY,SAAS,OAAgB;AACnC,QAAI,KAAK,cAAc,OAAO;AAC5B,WAAK,YAAY;AACjB,WAAK,gBAAgB;IACvB;EACF;;;;EAKA,IAAI,WAAW;AACb,WAAO,KAAK,gBAAgB;EAC9B;;;;EAKA,IAAI,YAAY;AACd,QAAI,YAAY;AAChB,QAAI,KAAK,UAAW,cAAa;AACjC,QAAI,KAAK,SAAU,cAAa;AAChC,QAAI,KAAK,YAAa,cAAa;AACnC,QAAI,KAAK,gBAAgB,SAAU,cAAa;AAChD,WAAO;EACT;EAEQ,kBAAkB;AACxB,SAAK,iBAAiB;AAEtB,SAAK,YAAY,YACf,GAAG,KAAK,YAAY,SAAS,IAAI,KAAK,SAAS,GAAG,KAAK;EAC3D;EAEQ,mBAAmB;AACzB,SAAK,YAAY,YAAY,KAAK,YAAY,UAC3C,QAAQ,iBAAiB,EAAE,EAC3B,KAAK;EACV;AACF;;;ACh1BA,mBAQO;ACTP,IAAAE,gBAAoC;ADqIxB,yBAAA;AEpIZ,IAAAA,gBAAsC;ADG/B,IAAM,QAAN,MAAe;EAGpB,YAAoB,OAAU;AAFtB,qCAA2B,CAAC;AAEhB,SAAA,QAAA;EAAW;EAE/B,IAAI,OAAU;AACZ,SAAK,QAAQ;AAEb,aAAS,YAAY,KAAK,WAAW;AACnC,eAAS,KAAK,KAAK;IACrB;EACF;EAEA,UAAU,UAAuB;AAC/B,SAAK,YAAY,CAAC,GAAG,KAAK,WAAW,QAAQ;AAC7C,WAAO,MAAM;AACX,WAAK,YAAY,KAAK,UAAU,OAAO,CAAC,MAAM,MAAM,QAAQ;IAC9D;EACF;EAEA,MAAM;AACJ,WAAO,KAAK;EACd;AACF;AAEO,SAAS,SAAY,OAAiB;AAC3C,QAAM,CAAC,OAAO,QAAQ,QAAI,wBAAS,MAAM,IAAI,CAAC;AAE9C,+BAAU,MAAM;AACd,WAAO,MAAM,UAAU,CAACC,WAAU,SAASA,MAAK,CAAC;EACnD,GAAG,CAAC,KAAK,CAAC;AAEV,SAAO;AACT;ADxBO,IAAM,mBAAe,4BAAwC,IAAI;AAQjE,IAAM,wBAAwB,IAAI,MAAgC,IAAI;AAKtE,IAAM,iBAAa;EACxB,CACE;IACE;IACA,OAAO;IACP,UAAU,CAAC;IACX;IACA,UAAU;IACV;IACA;EACF,GACA,QACG;AACH,UAAM,iBAAa,qBAA8B,IAAI;AACrD,UAAM,iBAAa,qBAA8B,IAAI;AAErD,UAAM,CAAC,OAAO,QAAQ,QAAIC,aAAAA,UAA4B,MAAS;AAG/D;MACE;MACA,OAAO;QACL,SAAS,WAAW;QACpB,SAAS,WAAW;QACpB;MACF;MACA,CAAC,KAAK;IACR;AAGAC,qBAAAA,WAAU,MAAM;AACd,YAAMC,SAAQ,IAAI,MAAM;QACtB,GAAG;QACH,GAAI,CAAC,QAAQ;UACX,SAAS,WAAW;UACpB,SAAS,WAAW;QACtB;QACA,UAAS,mCAAS,YAAW;;MAC/B,CAAC;AAED,eAASA,MAAK;AAEd,aAAO,MAAM;AACXA,eAAM,QAAQ;AACd,iBAAS,MAAS;MACpB;IACF,GAAG,CAAC,MAAM,KAAK,UAAU,OAAO,CAAC,CAAC;AAGlC,UAAM,oBAAgB,qBAKpB,CAAC,CAAC;AAEJ,UAAM,kBAAgD;MACpD,CAAC,UAAU,aAAa;AACtB,sBAAc,QAAQ,KAAK,EAAE,UAAU,SAAS,CAAC;AACjD,sBAAc,QAAQ,KAAK,CAAC,GAAG,MAAM,EAAE,WAAW,EAAE,QAAQ;MAC9D;MACA,CAAC;IACH;AAEA,UAAM,qBAAsD;MAC1D,CAAC,aAAa;AACZ,sBAAc,UAAU,cAAc,QAAQ;UAC5C,CAAC,OAAO,GAAG,aAAa;QAC1B;MACF;MACA,CAAC;IACH;AAGAD,qBAAAA,WAAU,MAAM;AACd,UAAI,QAAQ,OAAO;AACjB,8BAAsB,IAAI,EAAE,OAAO,aAAa,eAAe,CAAC;AAEhE,eAAO,MAAM,sBAAsB,IAAI,IAAI;MAC7C;IACF,GAAG,CAAC,MAAM,OAAO,aAAa,cAAc,CAAC;AAG7CA,qBAAAA,WAAU,MAAM;AACd,UAAI,CAAC,MAAO;AAEZ,YAAM,WAA2B,CAAC,SAAS;;AACzC,iBAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,QAAQ,KAAK;AACrD,8BAAc,QAAQ,CAAC,MAAvB,mBAA0B,SAAS;QACrC;MACF;AAEA,YAAM,GAAG,UAAU,QAAQ;AAE3B,aAAO,MAAM;AACX,cAAM,IAAI,UAAU,QAAQ;MAC9B;IACF,GAAG,CAAC,KAAK,CAAC;AAEV,eACE;MAAC,aAAa;MAAb;QACC,OAAO,EAAE,OAAe,aAAa,eAAe;QAEnD,UAAA,OACC,eAEA,wBAAC,OAAA,EAAI,KAAK,YAAY,WAAsB,OAAe,GAAG,OAC5D,cAAA,wBAAC,OAAA,EAAI,KAAK,YAAa,SAAA,CAAS,EAAA,CAClC;MAAA;IAEJ;EAEJ;AACF;AEpIA,IAAM,kBAA8C,CAAC;AA0C9C,SAAS,SACd,UACA,OAAc,CAAC,GACf,WAAW,GACX;AAEA,QAAM,mBAAe,0BAAW,YAAY;AAE5C,QAAM,cAAc,SAAS,qBAAqB;AAElD,QAAM,iBAAiB,gBAAgB,eAAe;AAEtD,QAAM,EAAE,OAAO,aAAa,eAAe,IAAI;AAE/CA,oBAAAA,WAAU,MAAM;AACd,QAAI,CAAC,YAAY,CAAC,eAAe,CAAC,kBAAkB,CAAC,MAAO;AAE5D,gBAAY,UAAU,QAAQ;AAC9B,aAAS,KAAK;AAEd,WAAO,MAAM;AACX,qBAAe,QAAQ;IACzB;EACF,GAAG,CAAC,OAAO,aAAa,gBAAgB,UAAU,GAAG,IAAI,CAAC;AAE1D,SAAO;AACT;", "names": ["lerp", "_a", "import_react", "state", "useState", "useEffect", "lenis"]}