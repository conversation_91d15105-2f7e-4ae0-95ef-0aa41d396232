# Project Context - CF-NextJS Portfolio

## Current Issue
- **Problem**: "window is not defined" error in Hero.tsx component
- **Cause**: Accessing window object during SSR (Server-Side Rendering) in Next.js
- **Location**: Line 113 in Hero.tsx where `window.innerWidth` is used directly in JSX

## Project Structure
- Next.js React application
- TypeScript enabled
- Portfolio website with Hero component
- Using Prism.js for code highlighting
- Custom UI components with animations

## Recent Changes
- Previously resolved TypeScript errors in UI components
- Removed `any` types and `@ts-expect-error` comments
- Implemented proper type safety

## Current Task
- ✅ Fixed SSR window access issue in Hero.tsx
- ✅ Implemented proper client-side only execution for window-dependent code
- ✅ Added proper TypeScript types and safety checks

## Package Manager
- Using pnpm (confirmed by pnpm-lock.yaml)

## Changes Made
1. **Added client-side state management**:
   - `isClient` state to track if component is mounted on client
   - `windowDimensions` state to store window width/height safely

2. **Updated useEffect**:
   - Added `typeof window !== 'undefined'` checks
   - Implemented proper window dimension tracking
   - Added cleanup for event listeners

3. **Fixed JSX window access**:
   - Replaced direct `window.innerWidth` access with state-based approach
   - Added `isClient` check to prevent SSR issues

## Result
- No more "window is not defined" errors
- Proper SSR compatibility
- Maintained responsive behavior for 1366x768 resolution 