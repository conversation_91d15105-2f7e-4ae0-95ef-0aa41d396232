@tailwind base;
@tailwind components;
@tailwind utilities;

@keyframes float-slow {
  0%,
  100% {
    transform: translateY(0) rotate(1deg);
  }
  50% {
    transform: translateY(-10px) rotate(-1deg);
  }
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.gradient-text {
  background: linear-gradient(90deg, #60a5fa, #34d399, #60a5fa);
  background-size: 200% auto;
  animation: gradient-shift 4s linear infinite;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.gradient-border {
  position: relative;
  background: linear-gradient(45deg, #60a5fa, #34d399);
  padding: 2px;
  border-radius: 0.75rem;
}

.nav-link {
  position: relative;
}

.nav-link::after {
  content: "";
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -4px;
  left: 0;
  background: linear-gradient(45deg, #60a5fa, #34d399);
  transition: width 0.3s ease;
}

.nav-link:hover::after {
  width: 100%;
}

.code-window {
  position: relative;
  border-radius: 0.75rem;
  overflow: hidden;
  transition: all 0.3s ease;
}

.window-header {
  background: #0b466359;
  padding: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.window-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  transition: transform 0.3s ease;
}

.window-dot:hover {
  transform: scale(1.2);
}

.animate-float-slow {
  animation: float-slow 7s ease-in-out infinite;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}

.typing-effect::after {
  content: "|";
  animation: blink 1s step-end infinite;
  margin-left: 2px;
  color: #60a5fa;
}

@keyframes blink {
  from,
  to {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}

/* Enhanced Prism.js styling */
pre[class*="language-"] {
  background: #091121 !important;
  padding: 1.5rem !important;
  margin: 0 !important;
  font-size: 0.95rem !important;
}

.token.comment {
  color: #8b9cb3 !important;
  font-style: italic;
}

.token.string {
  color: #a5d6ff !important;
}

.token.keyword {
  color: #ff79c6 !important;
}

.token.function {
  color: #66e7ff !important;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
/* Add these classes to adjust the grid colors in the top-left and bottom-right */
.top-left-grid {
  background-color: white;
  opacity: 0.8; /* Make it more visible */
}

.bottom-right-grid {
  background-color: white;
  opacity: 0.8; /* Make it more visible */
}
